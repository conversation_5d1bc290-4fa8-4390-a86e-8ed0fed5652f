<?php

/**
 * @file
 * Contains luxair_megadropdown.module..
 */

use <PERSON>upal\Component\Utility\NestedArray;
use <PERSON>upal\Core\Cache\Cache;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON>upal\Core\Language\LanguageInterface;
use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;
use Drupal\Core\Url;
use <PERSON><PERSON><PERSON>\node\Entity\Node;

/**
 * Implements hook_help().
 */
function luxair_megadropdown_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Main module help for the luxair_megadropdown module.
    case 'help.page.luxair_megadropdown':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('Luxair Megadropdown menu') . '</p>';
      return $output;

    default:
  }
}


/**
 * Alter the menu_link form in order to add additional information.
 *
 * Implements hook_form_FORM_ID_alter().
 */
function luxair_megadropdown_form_menu_link_content_menu_link_content_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  $menuParent = isset($form['menu_parent']) ? (isset($form['menu_parent']['#default_value']) ? $form['menu_parent']['#default_value'] : '') : '';
  // Add the right functionality per menu.
  $entity = $form_state->getFormObject()->getEntity();
  if ($menuParent == 'main:'|| $menuParent == 'corporate:') {
    luxair_megadropdown_add_megadropdown_menu_info($form, $entity);
  }
}

/**
 * Add the Megadropdown block to the form and the different settings.
 *
 * @param $form
 * @param $entity
 */
function luxair_megadropdown_add_megadropdown_menu_info(&$form, $entity) {
  // Check if we're in a translation context and hide untranslatable fields.
  $route_match = \Drupal::routeMatch();
  if (
    $route_match->getRouteName() == 'entity.menu_link_content.content_translation_add'
    || (
      $route_match->getRouteName() == 'entity.menu_link_content.canonical'
      && \Drupal::languageManager()->getCurrentLanguage()->getId() !== \Drupal::languageManager()->getDefaultLanguage()->getId()
    )
  ) {
    return;
  }

  $link = $entity->get('link');
  $attachedBlock = '';
  $config = \Drupal::config('luxair_megadropdown.settings');
  $ajax = $config->get('ajax_load');
  if (!$link->isEmpty()) {
    $linkOptions = isset($link->getValue()[0]['options']) ? $link->getValue()[0]['options'] : [];
    $menuAttachBlock = isset($linkOptions['menu_attach_block']) ? $linkOptions['menu_attach_block'] : [];
    $attachedBlock = isset($menuAttachBlock['block']) ? $menuAttachBlock['block'] : '';
  }
  // Megadropdown & menu - Extra form fields
  $form['menu_attach_block'] = array(
    '#type' => 'fieldset',
    '#title' => t('Megadropdown Block'),
    '#description' => t('Choose a block to be rendered as part of this menu item.'),
    '#collapsible' => FALSE,
    '#collapsed' => FALSE,
    '#prefix' => '<div id="menu-block">',
    '#suffix' => '</div>',
    '#tree' => TRUE,
  );
  $form['menu_attach_block']['block'] = array(
    '#type' => 'select',
    '#title' => t('Megadropdown Block'),
    '#empty_option' => t('- None -'),
    '#description' => t('Select a block to attach.'),
    '#default_value' => !empty($attachedBlock) ? $attachedBlock : '',
    '#options' => luxair_megadropdown_get_all_megadropdown_blocks(),
  );
  $form['menu_attach_block']['ajax'] = array(
    '#type' => 'hidden',
    '#title' => t('Load with Ajax'),
    '#description' => t('Load the block using Ajax when the user clicks on the menu link.'),
    '#default_value' => $ajax,
  );

  foreach (array_keys($form['actions']) as $action) {
    if ($action != 'preview' && isset($form['actions'][$action]['#type']) && $form['actions'][$action]['#type'] === 'submit') {
      array_unshift($form['actions'][$action]['#submit'], 'luxair_megadropdown_menu_edit_item_dropdown_submit');
    }
  }
}

/**
 * Get all megadropdown block ids
 * @return array
 */
function luxair_megadropdown_get_all_megadropdown_blocks() {
  $query = \Drupal::entityQuery('block_content')
    ->accessCheck(FALSE)
    ->condition('type', 'megadropdown');
  $bids = $query->execute();
  $blocks = [];
  if ($bids) {
    $storage = \Drupal::entityTypeManager()->getStorage('block_content');
    $megaBlocks = $storage->loadMultiple($bids);
    foreach ($megaBlocks as $delta => $block) {
      $blocks[$delta] = $block->info->getValue()[0]['value'];
    }
  }

  return $blocks;
}

/**
 * Submit handler for menu_edit_item form.
 *
 * Save the megadropdown block.
 */
function luxair_megadropdown_menu_edit_item_dropdown_submit($form, &$form_state) {
  // Save menu_attach_blocks values in the links options.
  $links = $form_state->getValue('link');
  $links[0]['options']['menu_attach_block'] = $form_state->getValue('menu_attach_block');
  $form_state->setValue('link', $links);
}

/**
 * @param $variables
 * @param $hook
 */
function luxair_megadropdown_preprocess_menu__main(&$variables, $hook) {
  $config = \Drupal::config('luxair_megadropdown.settings');
  $ajax = $config->get('ajax_load');
  $items = $variables['items'];
  $cache_tags = \Drupal::entityTypeManager()->getDefinition('entity_subqueue')->getListCacheTags();
  $block_render = [];

  foreach ($items as $key => $item) {
    // If there is a dropdown block attached to the menu item
    if (!empty($item['url']->getOptions()['menu_attach_block']['block'])) {
      $megaBlockRender = FALSE;
      $megadropdownAjax = $ajax;
      $attachedBlockId = NULL;

      // Loading the megadropdown block if it is attached to the menu item
      $query = \Drupal::entityQuery('menu_link_content')
        ->accessCheck(FALSE)
        ->condition('title', $item['original_link']->getPluginDefinition()['title'])
        ->condition('menu_name', 'main');
      $itemId = $query->execute();
      if ($itemId) {
        $storage = \Drupal::entityTypeManager()
          ->getStorage('menu_link_content');
        $menuLinkContent = $storage->load(array_values($itemId)[0]);
        if ($menuLinkContent) {
          // If there's a block attached we don't use the link itself.
          $variables['items'][$key]['url'] = '#';
          $link = !$menuLinkContent->link->isEmpty() ? $menuLinkContent->link->getValue() : [];
          $options = isset($link[0]['options']) ? $link[0]['options'] : [];
          $menuAttachBlock = isset($options['menu_attach_block']) ? $options['menu_attach_block'] : [];
          $attachedBlockId = isset($menuAttachBlock['block']) ? $menuAttachBlock['block'] : NULL;
        }
      }
      if ($attachedBlockId && !$megadropdownAjax) {
        $storage = \Drupal::entityTypeManager()->getStorage('block_content');
        $megaBlock = $storage->load($attachedBlockId);
        if ($megaBlock) {
          $block_render = \Drupal::entityTypeManager()
            ->getViewBuilder('block_content')
            ->view($megaBlock, 'megadropdown');
          if (isset($block_render['#cache']['tags'])) {
            $cache_tags = Cache::mergeTags($cache_tags, $block_render['#cache']['tags']);
            $block_render['#cache']['tags'] = $cache_tags;
          }
        }
      }
      if ($block_render) {
        $variables['items'][$key]['megadropdownContent'] = [
          '#theme' => 'megadropdown_wrapper',
          '#field_paragraph_top' => $block_render['field_paragraph_top'],
          '#field_paragraph_middle' => $block_render['field_paragraph_middle'],
          '#field_paragraph_bottom' => $block_render['field_paragraph_bottom'],
          '#menu_item_machine_name' => $variables['items'][$key]['menuItemMachineName']
        ];
      }
      $variables['items'][$key]['megadropdownId'] = $attachedBlockId;
      $variables['items'][$key]['megadropdownAjax'] = $megadropdownAjax;
    }
  }

  $type = LanguageInterface::TYPE_INTERFACE;
  $route_match = \Drupal::getContainer()->get('current_route_match');
  $url = !\Drupal::service('path.matcher')->isFrontPage() && $route_match->getRouteObject()
    ? Url::fromRouteMatch($route_match)
    : Url::fromRoute('<front>');
  $links = \Drupal::getContainer()->get('language_manager')->getLanguageSwitchLinks($type, $url);
  $current = \Drupal::getContainer()->get('language_manager')->getCurrentLanguage();

  if (isset($links->links)) {
    $variables['language_links'] = array(
      '#cache' => [
        'contexts' => ['url.path'],
        'max-age' => 0,
      ],
      '#theme' => 'links__main_menu_language_links',
      '#links' => $links->links,
    );
    $variables['language_links']['#links'][$current->getId()]['current'] = TRUE;
  }
}

/**
 * @param $variables
 * @param $hook
 */
function luxair_megadropdown_preprocess_menu__corporate(&$variables, $hook) {
  $config = \Drupal::config('luxair_megadropdown.settings');
  $ajax = $config->get('ajax_load');
  $items = $variables['items'];
  $cache_tags = \Drupal::entityTypeManager()->getDefinition('entity_subqueue')->getListCacheTags();
  $block_render = [];

  foreach ($items as $key => $item) {
    // If there is a dropdown block attached to the menu item
    if (!empty($item['url']->getOptions()['menu_attach_block']['block'])) {
      $megaBlockRender = FALSE;
      $megadropdownAjax = $ajax;
      $attachedBlockId = NULL;

      // Loading the megadropdown block if it is attached to the menu item
      $query = \Drupal::entityQuery('menu_link_content')
        ->accessCheck(FALSE)
        ->condition('title', $item['original_link']->getPluginDefinition()['title'])
        ->condition('menu_name', 'corporate');
      $itemId = $query->execute();
      if ($itemId) {
        $storage = \Drupal::entityTypeManager()
          ->getStorage('menu_link_content');
        $menuLinkContent = $storage->load(array_values($itemId)[0]);
        if ($menuLinkContent) {
          // If there's a block attached we don't use the link itself.
          $variables['items'][$key]['url'] = '#';
          $link = !$menuLinkContent->link->isEmpty() ? $menuLinkContent->link->getValue() : [];
          $options = isset($link[0]['options']) ? $link[0]['options'] : [];
          $menuAttachBlock = isset($options['menu_attach_block']) ? $options['menu_attach_block'] : [];
          $attachedBlockId = isset($menuAttachBlock['block']) ? $menuAttachBlock['block'] : NULL;
        }
      }
      if ($attachedBlockId && !$megadropdownAjax) {
        $storage = \Drupal::entityTypeManager()->getStorage('block_content');
        $megaBlock = $storage->load($attachedBlockId);
        if ($megaBlock) {
          $block_render = \Drupal::entityTypeManager()
            ->getViewBuilder('block_content')
            ->view($megaBlock, 'megadropdown');
          if (isset($block_render['#cache']['tags'])) {
            $cache_tags = Cache::mergeTags($cache_tags, $block_render['#cache']['tags']);
            $block_render['#cache']['tags'] = $cache_tags;
          }
        }
      }
      if ($block_render) {
        $variables['items'][$key]['megadropdownContent'] = [
          '#theme' => 'megadropdown_wrapper',
          '#field_paragraph_top' => $block_render['field_paragraph_top'],
          '#field_paragraph_middle' => $block_render['field_paragraph_middle'],
          '#field_paragraph_bottom' => $block_render['field_paragraph_bottom'],
        ];
      }
      $variables['items'][$key]['megadropdownId'] = $attachedBlockId;
      $variables['items'][$key]['megadropdownAjax'] = $megadropdownAjax;
    }
  }
}


/**
 * Implements hook_theme().
 */
function luxair_megadropdown_theme() {
  $theme = [];
  $theme['megadropdown_wrapper'] = [
    'render element' => 'elements',
    'variables' => [
      'field_paragraph_top' => NULL,
      'field_paragraph_middle' => NULL,
      'field_paragraph_bottom' => NULL,
      'menu_item_machine_name' => NULL
    ],
  ];

  return $theme;
}

/**
 * Implements hook_theme_suggestions_alter().
 */
function luxair_megadropdown_theme_suggestions_alter(array &$suggestions, array $variables, $hook) {
  if ($hook == 'field' && isset($variables['element']) && ($variables['element']['#field_name'] == 'field_queue' || $variables['element']['#field_name'] == 'field_queues')) {
    if (isset($variables['element']) && $variables['element']['#view_mode'] === 'two_columns_entity_queue') {
      $suggestions[] = 'field__reset__paragraph__' .  $variables['element']['#field_name'] . '__' . $variables['element']['#view_mode'];
    }
    if (
      (array_key_exists('#destination', $variables['element']) &&
        isset($variables['element']['#destination']) &&
        $variables['element']['#destination'] === TRUE
      ) ||
      (array_key_exists('#destination', $variables['element'][0]) &&
        isset($variables['element'][0]['#destination']) &&
      $variables['element'][0]['#destination'] === TRUE
      )
    ) {
      $suggestions[] = 'field__block_content__field_queue__megadropdown__destinations';
    }
  } else {

  }
}

/**
 * Implements hook_module_implements_alter().
 */
function luxair_megadropdown_module_implements_alter(&$implementations, $hook) {
  if ($hook == 'theme_suggestions_alter') {
    $group = $implementations['luxair_megadropdown'];
    unset($implementations['luxair_megadropdown']);
    $implementations['luxair_megadropdown'] = $group;
  }
  if ($hook == 'module_implements_alter') {
    unset($implementations['ds']);
  }
}

/**
 * Implements hook_preprocess_HOOK().
 *
 * Reorder the destinations, and show them as two columns with a pager.
 */
function luxair_megadropdown_preprocess_field__block_content__field_queue__megadropdown__destinations(&$variables) {
  $langcode = \Drupal::languageManager()->getCurrentLanguage()->getId();

  $queueName = 'destinations';
  if (!$variables['element']['#items']->isEmpty()) {
    $queueName = $variables['element']['#items'][0]->target_id;
  }

  // We need to request all the destinations with the right order, taking into
  // account the different translations.
  $query = \Drupal::database()->select('node_field_data', 'n');
  $query->addField('n', 'nid');
  $query->addExpression('IF(country_language.name IS NOT NULL, country_language.name, tfd.name)', 'country_name');
  $query->leftJoin('node__field_country', 'fc', 'n.nid = fc.entity_id AND fc.deleted = 0 AND fc.langcode = n.langcode');
  $query->innerJoin('taxonomy_term_field_data', 'tfd', 'fc.field_country_target_id = tfd.tid');
  $query->innerJoin('entity_subqueue__items', 'qi', 'n.nid = qi.items_target_id AND qi.entity_id = :queue', [':queue' => $queueName]);
  $query->leftJoin('taxonomy_term_field_data', 'country_language', 'fc.field_country_target_id = country_language.tid AND country_language.langcode = :lang', [':lang' => $langcode]);
  $query->condition('n.status', 1)
    ->condition('n.type', 'destination')
    ->condition('n.default_langcode', 1)
    ->condition('tfd.default_langcode', 1);
  $query->orderBy('country_name', 'ASC');
  $query->orderBy('n.title', 'ASC');

  $result = $query->execute();
  $results = [];
  foreach ($result as $row) {
    $results[] = $row;
  }

  // We separate the results by page first.
  $npp = 12;
  if ($results) {
    $total = count($results);
    $pages = ceil($total / $npp);
    $remainder = $total % $npp;
    if ($remainder == 0) {
      $remainder = $npp;
    }
    $variables['items'] = [];

    for ($i = 0, $j = 0; $i < $pages; $i++) {
      if ($i < ($pages - 1)) {
        $total = $npp;
      }
      else {
        $total = $remainder;
      }
      // We separate the items in two columns.
      $half = ceil($total / 2);
      $left = [];
      $right = [];
      for ($k = 0; $k < $total; $k++, $j++) {
        $node = Node::load($results[$j]->nid);
        if ($node->hasTranslation($langcode)) {
          $node = $node->getTranslation($langcode);
        }
        // We copy the cache info from the original items.
        $item = [
          'url' => $node->toUrl('canonical'),
          'destination_name' => $node->getTitle(),
          'country_name' => $results[$j]->country_name,
          '#cache' => [
            'bin' => 'render',
            'contexts' => [
              'languages:language_content',
              'user.permissions',
            ],
            'keys' => [
              'entity_view',
              'node',
              $node->id(),
              'megadropdown',
              $node->language()->getId(),
            ],
            'max-age' => -1,
            'tags' => [
              'config:entityqueue.entity_queue.megadropdown',
              'entity_field_info',
              'entity_subqueue:' . $queueName,
              'node:' . $node->id(),
              'node_list',
              'node_view',
              'views_data',
            ],
          ],
        ];
        if ($k < $half) {
          $left[] = $item;
        }
        else {
          $right[] = $item;
        }
      }
      $variables['items'][$i] = [$left, $right];
    }
  }
  else {
    $variables['items'] = [[], []];
  }
}
