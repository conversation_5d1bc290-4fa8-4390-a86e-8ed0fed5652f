<?php

/**
 * @file
 * Contains luxair_drupal_commons.module..
 */
use Drupal\Component\Utility\Environment;
use Drupal\Component\Utility\NestedArray;
use Drupal\Core\Form\FormState;
use Drupal\Core\Form\FormStateInterface;
use <PERSON>upal\Core\Render\Markup;
use Drupal\Core\Routing\RouteMatchInterface;
use Drupal\Core\Url;
use Drupal\file\Entity\File;
use Drupal\node\NodeInterface;


/**
 * Implements hook_help().
 */
function luxair_drupal_commons_help($route_name, RouteMatchInterface $route_match)
{
  switch ($route_name) {
      // Main module help for the luxair_drupal_commons module.
    case 'help.page.luxair_drupal_commons':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('Luxair drupal common') . '</p>';
      return $output;

    default:
  }
}


/**
 * Implements hook_theme().
 */
function luxair_drupal_commons_theme()
{
  $theme = [];
  $theme['teaser_svg_icon'] = [
    'variables' => [
      'svg' => NULL
    ]
  ];
  $theme['taxonomy_term__default_country'] = [
    'render element' => 'elements',
  ];
  $theme['links__site_selector'] = [
    'template' => 'links__site_selector',
    'variables' => [
      'links' => NULL,
    ],
  ];
  $theme['lg_dropdown'] = [
    'render element' => 'element',
  ];
  $theme['lg_textfield'] = [
    'render element' => 'element'
  ];
  $theme['lg_popoverfield'] = [
    'render element' => 'element'
  ];
  // The lg Date picker form element.
  $theme['lg_date_input'] = [
    'render element' => 'element'
  ];

  return $theme;
}

/**
 * Implements hook_field_widget_form_alter().
 */
function luxair_drupal_commons_field_widget_form_alter(&$element, FormStateInterface $form_state, $context)
{
  if ($context['widget']->getPluginId() === 'image_image') {
    $element['#process'][] = 'luxair_drupal_commons_image_image_process';
  }
}

/**
 * Process function.
 *
 * @param $element
 * @param \Drupal\Core\Form\FormStateInterface $form_state
 * @param $context
 * @return mixed
 */
function luxair_drupal_commons_image_image_process($element, FormStateInterface $form_state, $context)
{
  $element['title']['#title'] = t('Tooltip');
  return $element;
}

/**
 * Prepares variables for languages part of the block.
 *
 * @param array $variables
 */
function luxair_drupal_commons_preprocess_links__lgit_language_block(&$variables)
{
  _luxair_drupal_commons_remove_query_param_language_links($variables);
}

/**
 * Prepares variables for languages part of the block.
 *
 * @param array $variables
 */
function luxair_drupal_commons_preprocess_links__main_menu_language_links(&$variables)
{
  _luxair_drupal_commons_remove_query_param_language_links($variables);
}

function _luxair_drupal_commons_remove_query_param_language_links(&$variables) {
  foreach ($variables['links'] as $key => $row) {
    if (isset($row['link']['#url'])) {
      // Remove query parameters because they are breaking the static site:
      // The static generation makes a HTML copy for every page and if we have
      // the same page opened with query parameters it gets scraped and it
      // overrides the non-query page and the query parameters stay forever. For
      // example if we have the page /en/abount-us and somewhere within the site
      // we have this URL address /en/abount-us?query=parameter (as it happens
      // if we have query parameters within the language selector) both pages
      // are scraped and the second one overrides the first one. And we end up
      // with language selector with query parameters inside.
      $row['link']['#options']['query'] = [];
      // Replicate logic from Drupal link formatter, since the link is not rendered in the template
      $options = NestedArray::mergeDeep($row['link']['#url']->getOptions(), $row['link']['#options']);
      $row['link']['#url']->setOptions($options);
    }

    if (isset($row['link']['#options']['current'])) {
      $variables['current'] = $variables['title'] = $key;
    }
  }
}

/**
 * Build the link for the country selector.
 *
 * @param $variables
 */
function luxair_drupal_commons_preprocess_taxonomy_term__lgit_countries(&$variables)
{
  $code = $variables['term']->field_code->value;
  $variables['link'] = Url::fromRoute('luxair_drupal_commons.default_controller_saveCountry', ['code' => $code], ['query' => ['destination' => \Drupal::destination()->get()]]);
}


/**
 * Implements hook_form_FORM_ID_alter().
 *
 * Alter the menu_link form in order to add a dropdown
 * where the user can set some extra options.
 */
function luxair_drupal_commons_form_menu_link_content_menu_link_content_form_alter(&$form, FormStateInterface $form_state, $form_id)
{
  $menuParent = isset($form['menu_parent']) ? (isset($form['menu_parent']['#default_value']) ? $form['menu_parent']['#default_value'] : '') : '';

  $entity = $form_state->getFormObject()->getEntity();
  if ($menuParent == 'main:' || $menuParent == 'corporate:') {
    luxair_drupal_commons_add_machinename_menu_info($form, $entity);
  }
  luxair_drupal_commons_add_image_menu_info($form, $entity);

  // Add a hidden field to store the original link options.
  $form['#validate'] = array_merge(array('luxair_drupal_commons_menu_content_link_translation_validate'), $form['#validate']);
  $link = $entity->get('link');
  if (!$link->isEmpty()) {
    $linkOptions = isset($link->getValue()[0]['options']) ? $link->getValue()[0]['options'] : [];
    $form['options'] = [
      '#type' => 'value',
      '#value' => $linkOptions,
    ];
  }
}

/**
 * Add the machine name field.
 *
 * @param $form
 * @param $entity
 */
function luxair_drupal_commons_add_machinename_menu_info(&$form, $entity)
{
  // Check if we're in a translation context and hide untranslatable fields.
  $route_match = \Drupal::routeMatch();
  if (
    $route_match->getRouteName() == 'entity.menu_link_content.content_translation_add'
    || (
      $route_match->getRouteName() == 'entity.menu_link_content.canonical'
      && \Drupal::languageManager()->getCurrentLanguage()->getId() !== \Drupal::languageManager()->getDefaultLanguage()->getId()
    )
  ) {
    return;
  }

  $link = $entity->get('link');
  if (!$link->isEmpty()) {
    $linkOptions = isset($link->getValue()[0]['options']) ? $link->getValue()[0]['options'] : [];
    $menuAttachBlock = isset($linkOptions['menu_extra_options']) ? $linkOptions['menu_extra_options'] : [];
    $menuItemName = isset($menuAttachBlock['menuItemMachineName']) ? $menuAttachBlock['menuItemMachineName'] : '';
  }

  $form['menu_extra_options'] = array(
    '#type' => 'fieldset',
    '#title' => t('Menu extra config'),
    '#collapsible' => FALSE,
    '#collapsed' => FALSE,
    '#prefix' => '<div id="menu-extra">',
    '#suffix' => '</div>',
    '#tree' => TRUE,
  );
  $form['menu_extra_options']['menuItemMachineName'] = array(
    '#type' => 'textfield',
    '#maxlength' => 255,
    '#title' => t('Machine name'),
    '#required' => true,
    '#description' => t('Write a name which helps to identify the menu item. The machine-name cannot contain spaces.'),
    '#default_value' => !empty($menuItemName) ? $menuItemName : '',
  );

  $form['#validate'] = array_merge(array('luxair_drupal_commons_menu_edit_item_machinename_validate'), $form['#validate']);
  foreach (array_keys($form['actions']) as $action) {
    if ($action != 'preview' && isset($form['actions'][$action]['#type']) && $form['actions'][$action]['#type'] === 'submit') {
      array_unshift($form['actions'][$action]['#submit'], 'luxair_drupal_commons_menu_edit_item_machinename_submit');
    }
  }
}

/**
 * Validate handler for menu_edit_item form.
 *
 * Set the options back to the link from the origin translation language.
 * If not we have an error "Non-translatable fields can only be translated
 * from the original language."
 *
 * @param $form
 * @param $form_state
 */
function luxair_drupal_commons_menu_content_link_translation_validate($form, &$form_state)
{
  $links = $form_state->getValue('link');
  $links[0]['options'] = $form_state->getValue('options');
  $form_state->setValue('link', $links);
}

/**
 * Add the image field to the menu item.
 *
 * @param $form
 * @param $entity
 */
function luxair_drupal_commons_add_image_menu_info(&$form, $entity)
{
  // Check if we're in a translation context and hide untranslatable fields.
  $route_match = \Drupal::routeMatch();
  if (
    $route_match->getRouteName() == 'entity.menu_link_content.content_translation_add'
    || (
      $route_match->getRouteName() == 'entity.menu_link_content.canonical'
      && \Drupal::languageManager()->getCurrentLanguage()->getId() !== \Drupal::languageManager()->getDefaultLanguage()->getId()
    )
  ) {
    return;
  }

  $link = $entity->get('link');
  $menuAttachImage = [];
  if (!$link->isEmpty()) {
    $linkOptions = isset($link->getValue()[0]['options']) ? $link->getValue()[0]['options'] : [];
    $menuAttachImage = isset($linkOptions['menu_attach_image']) ? $linkOptions['menu_attach_image'] : [];
  }

  // Extra field.
  $form['menu_attach_image'] = [
    '#type' => 'managed_file',
    '#title' => t('Image'),
    '#upload_validators' => [
      'file_validate_extensions' => ['gif png jpg jpeg svg'],
      'file_validate_size' => [Environment::getUploadMaxSize()],
    ],
    '#default_value' => $menuAttachImage,
    '#upload_location' => 'public://menu',
  ];

  foreach (array_keys($form['actions']) as $action) {
    if ($action != 'preview' && isset($form['actions'][$action]['#type']) && $form['actions'][$action]['#type'] === 'submit') {
      array_unshift($form['actions'][$action]['#submit'], 'luxair_drupal_commons_menu_edit_item_image_submit');
    }
  }
}

/**
 * Submit handler for menu_edit_item form.
 *
 * Save the image.
 */
function luxair_drupal_commons_menu_edit_item_image_submit($form, &$form_state)
{
  $links = $form_state->getValue('link');
  $links[0]['options']['menu_attach_image'] = $form_state->getValue('menu_attach_image');
  $form_state->setValue('link', $links);
}

/**
 * Validate menu_edit_item form.
 */
function luxair_drupal_commons_menu_edit_item_machinename_validate($form, &$form_state)
{
  if ($form_state->isSubmitted()) {
    $menuItem = $form_state->getFormObject()->getEntity();
    $menuExtraOptions = &$form_state->getValue('menu_extra_options');
    if (isset($menuExtraOptions['menuItemMachineName'])) {
      if (empty($menuExtraOptions['menuItemMachineName'])) {
        $form_state->setErrorByName('menu_extra_options[menuItemMachineName]', t('You must provide a machine name to identify this menu item'));
      } else {
        // Validate the machine-name
        // - must be unique so we compare it with all the menu items
        // - cannot contain spaces
        $machineName = $menuExtraOptions['menuItemMachineName'];
        if (strpos($machineName, ' ') !== false) {
          $form_state->setErrorByName('menu_extra_options[menuItemMachineName]', t('The machine name cannot contain spaces. Please remove the spaces and submit the form again.'));
        }

        $query = \Drupal::entityQuery('menu_link_content')
          ->condition('menu_name', array('main', 'corporate'), 'IN')
          ->accessCheck(FALSE);
        $itemsId = $query->execute();
        $storage = \Drupal::entityTypeManager()->getStorage('menu_link_content');
        $menuLinks = $storage->loadMultiple($itemsId);
        foreach ($menuLinks as $item) {
          if ($item->id() == $menuItem->id()) {
            continue;
          }
          $link = !$item->link->isEmpty() ? $item->link->getValue() : [];
          $options = isset($link[0]['options']) ? $link[0]['options'] : [];
          $menuAttachBlock = isset($options['menu_extra_options']) ? $options['menu_extra_options'] : [];
          if (isset($menuAttachBlock['menuItemMachineName']) && $menuAttachBlock['menuItemMachineName'] == $menuExtraOptions['menuItemMachineName']) {
            $form_state->setErrorByName('menu_extra_options[menuItemMachineName]', t('The machine name must be unique. It seems that other menu link already has this machine name.'));
          }
        }
      }
    }
  }
}

/**
 * Submit handler for menu_edit_item form.
 */
function luxair_drupal_commons_menu_edit_item_machinename_submit($form, &$form_state)
{
  $links = $form_state->getValue('link');
  $links[0]['options']['menu_extra_options'] = $form_state->getValue('menu_extra_options');
  $form_state->setValue('link', $links);
}

/**
 * @param $variables
 * @param $hook
 */
function luxair_drupal_commons_preprocess_menu__main(&$variables, $hook)
{
  $items = $variables['items'];
  foreach ($items as $key => $item) {
    $menuItemName = NULL;
    $attachedBlockId = NULL;

    // get the menu_extra_options
    $query = \Drupal::entityQuery('menu_link_content')
      ->accessCheck(FALSE)
      ->condition('title', $item['original_link']->getPluginDefinition()['title'])
      ->condition('menu_name', 'main');
    $itemId = $query->execute();
    if ($itemId) {
      $storage = \Drupal::entityTypeManager()
        ->getStorage('menu_link_content');
      $menuLinkContent = $storage->load(array_values($itemId)[0]);
      if ($menuLinkContent) {
        $link = !$menuLinkContent->link->isEmpty() ? $menuLinkContent->link->getValue() : [];
        $options = isset($link[0]['options']) ? $link[0]['options'] : [];
        $menuAttachBlock = isset($options['menu_extra_options']) ? $options['menu_extra_options'] : [];
        $menuItemName = isset($menuAttachBlock['menuItemMachineName']) ? $menuAttachBlock['menuItemMachineName'] : '';
      }
      $variables['items'][$key]['menuItemMachineName'] = $menuItemName;
    }
  }
  $customblock = \Drupal::service('plugin.manager.block')->createInstance('burger_language_block', []);
  $variables['content']['custom_block_output'] = $customblock->build();
}

/**
 * @param $variables
 * @param $hook
 */
function luxair_drupal_commons_preprocess_menu__corporate(&$variables, $hook)
{
  $items = $variables['items'];
  foreach ($items as $key => $item) {
    $menuItemName = NULL;
    $attachedBlockId = NULL;

    // get the menu_extra_options
    $query = \Drupal::entityQuery('menu_link_content')
      ->accessCheck(FALSE)
      ->condition('title', $item['original_link']->getPluginDefinition()['title'])
      ->condition('menu_name', 'corporate');
    $itemId = $query->execute();
    if ($itemId) {
      $storage = \Drupal::entityTypeManager()
        ->getStorage('menu_link_content');
      $menuLinkContent = $storage->load(array_values($itemId)[0]);
      if ($menuLinkContent) {
        $link = !$menuLinkContent->link->isEmpty() ? $menuLinkContent->link->getValue() : [];
        $options = isset($link[0]['options']) ? $link[0]['options'] : [];
        $menuAttachBlock = isset($options['menu_extra_options']) ? $options['menu_extra_options'] : [];
        $menuItemName = isset($menuAttachBlock['menuItemMachineName']) ? $menuAttachBlock['menuItemMachineName'] : '';
      }
      $variables['items'][$key]['menuItemMachineName'] = $menuItemName;
    }
  }
}

/**
 * Implements hook_preprocess().
 *
 * Injects the image to each menu item.
 */
function luxair_drupal_commons_preprocess_menu(&$variables, $hook)
{
  $items = $variables['items'];
  $uuids = [];
  foreach ($items as $key => $item) {
    $uuids[] = str_replace('menu_link_content:', '', $key);
  }

  if ($uuids) {
    // Get the real entities.
    $storage = \Drupal::entityTypeManager()->getStorage('menu_link_content');
    $entities = $storage->loadByProperties(['uuid' => $uuids]);

    $images = [];
    foreach ($entities as $item) {
      $link = !$item->link->isEmpty() ? $item->link->getValue() : [];
      $options = isset($link[0]['options']) ? $link[0]['options'] : [];
      $menuAttachImage = isset($options['menu_attach_image']) ? $options['menu_attach_image'] : [];

      if ($menuAttachImage) {
        $images[$item->uuid()] = $menuAttachImage[0];
      } else {
        $variables['items']['menu_link_content:' . $item->uuid()]['image'] = '';
      }
    }

    if ($images) {
      $images_managed = File::loadMultiple($images);
      foreach ($images as $uuid => $fid) {
        if (isset($images_managed[$fid])) {
          /** @var File $file */
          $file = $images_managed[$fid];
          $uri = $file->getFileUri();
          $url = \Drupal::service('file_url_generator')->generateAbsoluteString($uri);
          $variables['items']['menu_link_content:' . $uuid]['image'] = \Drupal::service('file_url_generator')->transformRelative($url);
        } else {
          $variables['items']['menu_link_content:' . $uuid]['image'] = '';
        }
      }
    }
  }
}

/*
 * http://drupal.stackexchange.com/a/223452/56290
 */
function luxair_drupal_commons_form_user_login_form_alter(&$form, FormState $form_state)
{
  $form['#submit'][] = 'luxair_drupal_commons_user_login_submit';
}

function luxair_drupal_commons_user_login_submit(&$form, FormState $form_state)
{
  $request = \Drupal::service('request_stack')->getCurrentRequest();
  if ($request->request->has('destination')) {
    $request->query->set('destination', $request->request->get('destination'));
  } else {
    $form_state->setRedirect('system.admin_content');
  }
}

/**
 * @param array $css
 * @param $editor
 * Implements hook_editor_css_alter()
 * Add css from styleguide in Wysiwyg editor
 */
function luxair_drupal_commons_ckeditor_css_alter(array &$css, $editor)
{
  $css[] = \Drupal::service('extension.list.module')->getPath('luxair_styleguide') . '/build/styles/main.css';
}

/**
 * @param $variables
 * @param $hook
 * Implements hook_preprocess_HOOK()
 * Runs through the tables to add the "data-label" html5 attribute on TD's
 * This is for a better display of tables on mobiles.
 */
function luxair_drupal_commons_preprocess_paragraph__table_paragraph(&$variables, $hook)
{
  if (!empty($variables['content']['field_text'][0]['#text'])) {

    $DOM = new DOMDocument;
    // loadHTML does not understand UTF-8... we add it manually
    $DOM->loadHTML('<?xml encoding="UTF-8">' . $variables['content']['field_text'][0]['#text']);

    // dirty fix because
    foreach ($DOM->childNodes as $item)
      if ($item->nodeType == XML_PI_NODE)
        $DOM->removeChild($item); // remove hack
    $DOM->encoding = 'UTF-8'; // insert proper

    $DOM->preserveWhiteSpace = false;

    $headers = array();
    $header = $DOM->getElementsByTagName('th');
    if ($header->length) {
      foreach ($header as $item) {
        $headers[] = $item->nodeValue;
      }

      $trs = $DOM->getElementsByTagName('tbody')->item(0)->getElementsByTagName('tr');
      foreach ($trs as $i => $tr) {
        $tds = $tr->getElementsByTagName('td');
        foreach ($tds as $j => $td) {
          $td->setAttribute('data-label', $headers[$j]);
        }
      }
      $newText = $DOM->saveHTML();
      $variables['content']['field_text'][0]['#text'] = $newText;
    }
  }
}

function luxair_drupal_commons_preprocess_page(&$variables, $hook)
{
  // Let's create a block for B2B logo and link which is not cacheable.
  $is_anonymous = \Drupal::currentUser()->isAnonymous();
  $variables['content']['b2b_block_output'] = '';

  if (!$is_anonymous) {
    $membership_loader = \Drupal::service('group.membership_loader');
    $membership = $membership_loader->loadByUser(\Drupal::currentUser());
    if (!empty($membership[0])) {
      $block = \Drupal::service('plugin.manager.block')->createInstance('b2b_logo_block', []);
      $variables['content']['b2b_block_output'] = $block->build();
    }
  }
}

function luxair_drupal_commons_page_bottom(array &$page_bottom)
{

  $node = \Drupal::routeMatch()->getParameter('node');
  $admin_context = \Drupal::service('router.admin_context');

  if ($node instanceof NodeInterface && !$admin_context->isAdminRoute()) {
      $cybba_script_with_cookie_consent = <<<EOS
      <script>
        function attach_tag_to_dom_from_string(script_str){
          document.head.innerHTML = document.head.innerHTML + script_str;
        }
        var cybba_script = function(){var e=document.createElement("script");e.type="text/javascript",e.async=!0,e.src="//www.rtb123.com/tags/8e926075-7a81-4706-9627-a5376edbc232/btp.js";var t=document.getElementsByTagName("head")[0];t?t.appendChild(e,t):(t=document.getElementsByTagName("script")[0]).parentNode.insertBefore(e,t)};
        function isFullConsentGranted(){
          return !Didomi.shouldConsentBeCollected() && Didomi.getUserConsentStatusForAll().vendors.enabled.length === Didomi.getRequiredVendors().length && Didomi.getUserConsentStatusForAll().purposes.disabled.length === 0 ;
        }
        window.didomiOnReady = window.didomiOnReady || [];
        window.didomiOnReady.push(function (Didomi) {
          const consentStatus = isFullConsentGranted();
          if (consentStatus === true) {
            cybba_script();
          } else {
            Didomi.on('consent.changed', function () {
              const consentStatus = isFullConsentGranted();
              if(consentStatus == true) {
                cybba_script();
              }
            })
          }
        });
        </script>
EOS;
      $page_bottom['luxair_drupal_commons'] = [
        '#markup' => Markup::create($cybba_script_with_cookie_consent)
      ];
  }
}

function luxair_drupal_commons_form_teaser_application_travel_search_teaser_edit_form_alter(&$form, &$form_state, $form_id)
{
  $form['#attached']['library'][] = 'luxair_drupal_commons/luxair_drupal_commons_admin';
}

function luxair_drupal_commons_form_teaser_application_travel_search_teaser_form_alter(&$form, &$form_state, $form_id)
{
  $form['#attached']['library'][] = 'luxair_drupal_commons/luxair_drupal_commons_admin';
}

function luxair_drupal_commons_preprocess_video_embed_iframe__youtube(&$variables, $hook)
{
  $variables['vid'] = str_replace('https://www.youtube-nocookie.com/embed/', '', $variables['url']);
}

function luxair_drupal_commons_preprocess_field(&$variables, $hook)
{
  if ('text_long' === $variables['element']['#field_type']) {
    foreach ($variables['items'] as &$item) {
      if (
        !empty($item['content']['#type'])
        && 'processed_text' === $item['content']['#type']
        && FALSE !== stripos($item['content']['#text'], 'youtube.com/embed')
      ) {
        $item['content']['#text'] = str_replace(
          'youtube.com/embed',
          'youtube-nocookie.com/embed',
          $item['content']['#text']
        );
      }
    }
  }
}

function luxair_drupal_commons_page_attachments(array &$attachments) {
  $config = \Drupal::state()->get('luxair_flightsearch_history.settings');
  if (!empty($config)) {
    $attachments['#attached']['drupalSettings']['flight_history_params'] = [
      'flight_lifetype_type' => $config['flight_lifetype_type'],
      'flight_lifetime' => $config['flight_lifetime'],
      'flight_number' => $config['flight_number'],
      'travel_lifetype_type' => $config['travel_lifetype_type'],
      'travel_lifetime' => $config['travel_lifetime'],
      'travel_number' => $config['travel_number'],
      'car_lifetype_type' => $config['car_lifetype_type'],
      'car_lifetime' => $config['car_lifetime'],
      'car_number' => $config['car_number'],
    ];
  }

  // Show the current docker image build tag on the front page.
  if (
    \Drupal::service('path.matcher')->isFrontPage()
    && ($app_docker_image_build_tag_filename = \Drupal::root() . "/../app_docker_image_build_tag.txt")
    && file_exists($app_docker_image_build_tag_filename)
  ) {
    $attachments['#attached']['html_head'][] = [
      [
        '#type' => 'html_tag',
        '#tag' => 'meta',
        '#attributes' => [
          'name' => 'LuxairBuildTag',
          'content' => preg_replace(
            '/[^a-zA-Z0-9\-\_\.]/',
            '',
            trim(file_get_contents($app_docker_image_build_tag_filename)),
          ),
        ],
      ],
      'LuxairBuildTag',
    ];
  }
}
