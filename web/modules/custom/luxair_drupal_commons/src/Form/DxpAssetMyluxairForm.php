<?php

namespace Drupal\luxair_drupal_commons\Form;

use Drupal\Core\Config\TypedConfigManagerInterface;
use <PERSON>upal\Core\Form\ConfigFormBase;
use Drupal\Core\Form\FormStateInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\Core\Config\ConfigFactoryInterface;
use Psr\Log\LoggerInterface;

/**
 * Provides a DXP Assets form.
 */
class DxpAssetMyluxairForm extends ConfigFormBase
{
  /**
   * @var LoggerInterface
   */
  protected $logger;

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames()
  {
    return ['lgit.dxp_assets'];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId()
  {
    return 'dxp_asset_myluxair_form';
  }

  /**
   * Constructs a \Drupal\system\ConfigFormBase object.
   *
   * @param \Drupal\Core\Config\ConfigFactoryInterface $config_factory
   *   The factory for configuration objects.
   * @param \Drupal\Core\Config\TypedConfigManagerInterface $typedConfigManager
   *   The typed config manager.
   */
  public function __construct(ConfigFactoryInterface $config_factory, TypedConfigManagerInterface $typedConfigManager, LoggerInterface $logger)
  {
    parent::__construct($config_factory, $typedConfigManager);
    $this->logger = $logger;
  }


  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('config.factory'),
      $container->get('config.typed'),
      $container->get('logger.channel.default'),
    );
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state)
  {
    $version = $this->config('lgit.dxp_assets')->get('dxp_assets_myluxair_version');

    $form['asset_value'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Asset Version'),
      '#default_value' => $version,
      '#required' => TRUE,
    ];

    $form['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Save'),
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    parent::submitForm($form, $form_state);

    // Get the field value
    $value = $form_state->getValue('asset_value');

    // Save value in configuration
    $this->config('lgit.dxp_assets')
      ->set('dxp_assets_myluxair_version', $value)
      ->save();

    // Invalidate all the caches
    drupal_flush_all_caches();

    $this->logger->info('Asset version updated to: @value', ['@value' => $value]);
    // Confirmation message
    $this->messenger()->addMessage($this->t('The asset value has been saved.'));
  }
}
