# In order to to create pages it is necessary to define routes for them.
# A route maps a URL path to a controller. It defines what function
# or method will be called when a URL is accessed.
# If the user accesses http://drupal8.dev//saveCountry/{code}, the routing
# system will look for a route with that path. In this case it will find a
# match, and execute the _controller callback. In this case the callback is
# defined as a classname
# ("\Drupal\luxair_drupal_commons\Controller\DefaultController")
# and a method ("saveNewCountry").
luxair_drupal_commons.default_controller_saveCountry:
  path: '/saveCountry/{code}'
  defaults:
    _controller: '\Drupal\luxair_drupal_commons\Controller\CountriesController::saveCountry'
    _title: 'saveCountry'
  requirements:
    _permission: 'access content'

luxair_drupal_commons.mobile_app_config_default_version:
  path: '/mobile-app-config/{type}'
  defaults:
    _controller: '\Drupal\luxair_drupal_commons\Controller\MobileAppConfigController::defaultVersion'
    _title: 'Mobile app config'
  requirements:
    _permission: 'access content'
    type: 'home-offers.json|marketing-offers.json|menu-items.json|onboarding-teasers.json'

luxair_drupal_commons.mobile_app_config_language_version:
  path: '/mobile-app-config/{version}/{type}'
  defaults:
    _controller: '\Drupal\luxair_drupal_commons\Controller\MobileAppConfigController::languageVersion'
    _title: 'Mobile app config'
  requirements:
    _permission: 'access content'
    version: 'live|beta'
    type: 'home-offers.json|marketing-offers.json|menu-items.json|onboarding-teasers.json'

luxair_drupal_commons.admin:
  path: '/admin/config/luxair'
  defaults:
    _controller: '\Drupal\system\Controller\SystemController::systemAdminMenuBlockPage'
    _title: 'Luxair configuration'
  requirements:
    _permission: 'administer luxair'

luxair_drupal_commons.credit_card_secure_form:
  path: '/admin/config/luxair/3dsecure'
  defaults:
    _form: '\Drupal\luxair_drupal_commons\Form\CreditCardSecureForm'
    _title: '3d Secure configuration'
  requirements:
    _permission: 'administer 3dSecure'
  options:
    _admin_route: TRUE

luxair_drupal_commons.dxp_assets_shell:
  path: '/admin/config/luxair/dxp_asset_shell'
  defaults:
    _form: '\Drupal\luxair_drupal_commons\Form\DxpAssetShellForm'
    _title: 'DXP Shell version'
  requirements:
    _permission: 'administer luxair'
  options:
    _admin_route: TRUE

luxair_drupal_commons.dxp_assets_myluxair:
  path: '/admin/config/luxair/dxp_asset_myluxair'
  defaults:
    _form: '\Drupal\luxair_drupal_commons\Form\DxpAssetMyluxairForm'
    _title: 'DXP MyLuxair version'
  requirements:
    _permission: 'administer luxair'
  options:
    _admin_route: TRUE

luxair_drupal_commons.dxp_assets_myluxair_intelligent_search:

  path: '/admin/config/luxair/dxp_asset_myluxair_intelligent_search'
  defaults:
    _form: '\Drupal\luxair_drupal_commons\Form\DxpAssetIntelligentSearchForm'
    _title: 'DXP Intelligent Search version'
  requirements:
    _permission: 'administer luxair'
  options:
    _admin_route: TRUE

luxair_drupal_commons.flight_history_form:
  path: '/admin/config/luxair/search-history'
  defaults:
    _form: '\Drupal\luxair_drupal_commons\Form\FlightHistoryForm'
    _title: 'Flight History settings'
  requirements:
    _permission: 'administer luxair'
  options:
    _admin_route: TRUE
