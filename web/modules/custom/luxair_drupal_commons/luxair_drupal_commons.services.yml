services:
  luxair_drupal_commons.switches:
    class: Drupal\luxair_drupal_commons\SwitchesService
    arguments: ["@entity_type.manager", "@request_stack", "@cache_tags.invalidator"]
  luxair_drupal_commons.subscriber:
    class: Drupal\luxair_drupal_commons\EventSubscriber\LuxairDrupalCommonsEventSubscriber
    arguments:
      - "@current_user"
    tags:
      - { name: event_subscriber }
  luxair_drupal_commons.route_subscriber:
    class: Drupal\luxair_drupal_commons\Routing\RouteSubscriber
    tags:
     - { name: event_subscriber }
  luxair_drupal_commons.dxp_asset_shell_form:
    class: Drupal\luxair_drupal_commons\Form\DxpAssetShellForm
    arguments: ["@logger.channel.default", "@config.factory"]
  luxair_drupal_commons.dxp_asset_myluxair_form:
    class: Drupal\luxair_drupal_commons\Form\DxpAssetMyLuxairForm
    arguments: ["@logger.channel.default", "@config.factory"]
  luxair_drupal_commons.dxp_asset_myluxair_intelligent_search_form:
    class: Drupal\luxair_drupal_commons\Form\DxpAssetMyLuxairIntelligentSearchForm
    arguments: ["@logger.channel.default", "@config.factory"]
  luxair_drupal_commons.html_entity_decode_twig_extension:
    class: Drupal\luxair_drupal_commons\Twig\HtmlEntityDecodeTwigExtension
    arguments: ['@renderer']
    tags:
      - { name: twig.extension }
