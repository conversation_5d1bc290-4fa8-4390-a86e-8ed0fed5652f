<?php

namespace Drupal\luxair_static\Commands;

use <PERSON><PERSON><PERSON>\Core\Database\Connection;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\field\Entity\FieldStorageConfig;
use Drush\Commands\DrushCommands;

/**
 * Drush commands for analyzing paragraph usage across the site.
 */
final class ParagraphAnalysisCommand extends DrushCommands {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  private Connection $database;

  /**
   * Constructs a new ParagraphAnalysisCommand object.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Database\Connection $database
   *   The database connection.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager, Connection $database) {
    $this->entityTypeManager = $entity_type_manager;
    $this->database = $database;
  }

  /**
   * Analyze paragraph usage across all entity types.
   *
   * @command paragraph:analyze
   * @aliases para-analyze
   * @option format Output format (table, json, csv)
   * @option min-usage Minimum usage count to display (default: 0)
   * @option unused-only Show only unused paragraphs
   * @option show-orphaned Show paragraph types not configured in any field
   * @usage paragraph:analyze
   *   Show paragraph usage analysis in table format
   * @usage paragraph:analyze --format=json
   *   Show paragraph usage analysis in JSON format
   * @usage paragraph:analyze --unused-only
   *   Show only unused paragraph types
   * @usage paragraph:analyze --min-usage=5
   *   Show only paragraphs used 5 or more times
   * @usage paragraph:analyze --show-orphaned
   *   Show paragraph types that aren't configured in any field
   */
  public function analyzeParagraphUsage(
    $options = [
      'format' => 'table',
      'min-usage' => 0,
      'unused-only' => FALSE,
      'show-orphaned' => FALSE,
    ],
  ) {
    $this->output()->writeln('Analyzing paragraph usage across all entity types...');

    // Get all paragraph types.
    $paragraph_types = $this->getAllParagraphTypes();
    $this->output()->writeln(sprintf('Found %d paragraph types', count($paragraph_types)));

    // Get all paragraph fields.
    $paragraph_fields = $this->getAllParagraphFields();
    $this->output()->writeln(sprintf('Found %d paragraph fields', count($paragraph_fields)));

    // Show orphaned paragraph types if requested.
    if ($options['show-orphaned']) {
      $this->showOrphanedParagraphTypes($paragraph_types, $paragraph_fields);
      return;
    }

    // Analyze usage.
    $usage_data = $this->analyzeParagraphFieldUsage($paragraph_fields);

    // Filter results based on options.
    $filtered_data = $this->filterResults($usage_data, $options);

    // Output results.
    $this->outputResults($filtered_data, $paragraph_types, $options);
  }

  /**
   * Show orphaned paragraph types (not configured in any field).
   *
   * @param array $paragraph_types
   *   All paragraph types.
   * @param array $paragraph_fields
   *   All paragraph fields.
   */
  private function showOrphanedParagraphTypes(array $paragraph_types, array $paragraph_fields): void {
    // Get all configured paragraph types.
    $configured_types = [];

    foreach ($paragraph_fields as $field_storage) {
      $field_instances = $this->entityTypeManager
        ->getStorage('field_config')
        ->loadByProperties(['field_name' => $field_storage->getName()]);

      foreach ($field_instances as $instance) {
        $handler_settings = $instance->getSetting('handler_settings') ?? [];
        $target_bundles = $handler_settings['target_bundles'] ?? [];
        $configured_types = array_merge($configured_types, array_keys($target_bundles));
      }
    }

    $configured_types = array_unique($configured_types);
    $orphaned_types = [];

    foreach ($paragraph_types as $type_id => $paragraph_type) {
      if (!in_array($type_id, $configured_types)) {
        $orphaned_types[] = [
          'id' => $type_id,
          'label' => $paragraph_type->label(),
          'description' => $paragraph_type->getDescription(),
        ];
      }
    }

    if (empty($orphaned_types)) {
      $this->output()->writeln('<info>No orphaned paragraph types found. All paragraph types are configured in at least one field.</info>');
      return;
    }

    $this->output()->writeln(sprintf('<comment>Found %d orphaned paragraph types:</comment>', count($orphaned_types)));

    $rows = [];
    foreach ($orphaned_types as $type) {
      $rows[] = [
        $type['id'],
        $type['label'],
        $type['description'] ?: 'No description',
      ];
    }

    $this->io()->table(['Type ID', 'Label', 'Description'], $rows);
  }

  /**
   * Get all paragraph types.
   *
   * @return array
   *   Array of paragraph type entities keyed by ID.
   */
  private function getAllParagraphTypes(): array {
    return $this->entityTypeManager
      ->getStorage('paragraphs_type')
      ->loadMultiple();
  }

  /**
   * Get all paragraph fields across all entity types.
   *
   * @return array
   *   Array of field storage configurations for paragraph fields.
   */
  private function getAllParagraphFields(): array {
    $paragraph_fields = [];
    $field_storages = FieldStorageConfig::loadMultiple();

    foreach ($field_storages as $field_storage) {
      if ($field_storage->getType() === 'entity_reference_revisions' &&
          $field_storage->getSetting('target_type') === 'paragraph') {
        $paragraph_fields[] = $field_storage;
      }
    }

    return $paragraph_fields;
  }

  /**
   * Analyze paragraph field usage.
   *
   * @param array $paragraph_fields
   *   Array of paragraph field storage configurations.
   *
   * @return array
   *   Usage analysis data.
   */
  private function analyzeParagraphFieldUsage(array $paragraph_fields): array {
    $results = [];

    foreach ($paragraph_fields as $field_storage) {
      $field_name = $field_storage->getName();
      $entity_type = $field_storage->getTargetEntityTypeId();

      // Get all field instances for this field storage.
      $field_instances = $this->entityTypeManager
        ->getStorage('field_config')
        ->loadByProperties(['field_name' => $field_name]);

      foreach ($field_instances as $instance) {
        $bundle = $instance->getTargetBundle();
        $handler_settings = $instance->getSetting('handler_settings') ?? [];
        $target_bundles = $handler_settings['target_bundles'] ?? [];

        foreach ($target_bundles as $target_bundle) {
          $count = $this->countParagraphUsage($entity_type, $field_name, $target_bundle);

          $results[] = [
            'entity_type' => $entity_type,
            'bundle' => $bundle,
            'field_name' => $field_name,
            'paragraph_type' => $target_bundle,
            'reference_count' => $count,
          ];
        }
      }
    }

    return $results;
  }

  /**
   * Count paragraph usage for a specific type.
   *
   * @param string $entity_type
   *   The entity type.
   * @param string $field_name
   *   The field name.
   * @param string $paragraph_type
   *   The paragraph type.
   *
   * @return int
   *   The usage count.
   */
  private function countParagraphUsage(string $entity_type, string $field_name, string $paragraph_type): int {
    try {
      // Build the field table name.
      $table = $entity_type . '__' . $field_name;

      // Check if table exists.
      if (!$this->database->schema()->tableExists($table)) {
        return 0;
      }

      $query = $this->database->select($table, 't');
      $query->addExpression('COUNT(*)', 'count');
      $query->condition('t.' . $field_name . '_target_id', NULL, 'IS NOT NULL');

      // Join with paragraphs table to filter by type.
      $query->leftJoin('paragraphs_item_field_data', 'p', 'p.id = t.' . $field_name . '_target_id');
      $query->condition('p.type', $paragraph_type);

      return (int) $query->execute()->fetchField();
    }
    catch (\Exception $e) {
      $this->logger()->warning('Error counting paragraph usage: ' . $e->getMessage());
      return 0;
    }
  }

  /**
   * Filter results based on options.
   *
   * @param array $usage_data
   *   The usage data.
   * @param array $options
   *   The command options.
   *
   * @return array
   *   Filtered results.
   */
  private function filterResults(array $usage_data, array $options): array {
    $filtered = $usage_data;

    // Filter by minimum usage.
    if ($options['min-usage'] > 0) {
      $filtered = array_filter($filtered, function ($item) use ($options) {
        return $item['reference_count'] >= $options['min-usage'];
      });
    }

    // Filter for unused only.
    if ($options['unused-only']) {
      $filtered = array_filter($filtered, function ($item) {
        return $item['reference_count'] == 0;
      });
    }

    return $filtered;
  }

  /**
   * Output results in the specified format.
   *
   * @param array $filtered_data
   *   The filtered usage data.
   * @param array $paragraph_types
   *   All paragraph types.
   * @param array $options
   *   The command options.
   */
  private function outputResults(array $filtered_data, array $paragraph_types, array $options): void {
    if (empty($filtered_data)) {
      $this->output()->writeln('No results found matching the specified criteria.');
      return;
    }

    switch ($options['format']) {
      case 'json':
        $this->outputJson($filtered_data);
        break;

      case 'csv':
        $this->outputCsv($filtered_data);
        break;

      default:
        $this->outputTable($filtered_data, $paragraph_types);
        break;
    }

    // Show summary.
    $this->outputSummary($filtered_data, $paragraph_types);
  }

  /**
   * Output results as a table.
   *
   * @param array $data
   *   The usage data.
   * @param array $paragraph_types
   *   All paragraph types.
   */
  private function outputTable(array $data, array $paragraph_types): void {
    $rows = [];

    foreach ($data as $item) {
      $paragraph_label = isset($paragraph_types[$item['paragraph_type']])
        ? $paragraph_types[$item['paragraph_type']]->label()
        : $item['paragraph_type'];

      $rows[] = [
        $item['entity_type'],
        $item['bundle'],
        $item['field_name'],
        $item['paragraph_type'],
        $paragraph_label,
        $item['reference_count'],
      ];
    }

    // Sort by usage count (descending)
    usort($rows, function ($a, $b) {
      return $b[5] <=> $a[5];
    });

    $this->io()->table([
      'Entity Type',
      'Bundle',
      'Field Name',
      'Paragraph Type',
      'Paragraph Label',
      'Usage Count',
    ], $rows);
  }

  /**
   * Output results as JSON.
   *
   * @param array $data
   *   The usage data.
   */
  private function outputJson(array $data): void {
    $this->output()->writeln(json_encode($data, JSON_PRETTY_PRINT));
  }

  /**
   * Output results as CSV.
   *
   * @param array $data
   *   The usage data.
   */
  private function outputCsv(array $data): void {
    $output = fopen('php://output', 'w');

    // Header.
    fputcsv($output, [
      'Entity Type',
      'Bundle',
      'Field Name',
      'Paragraph Type',
      'Usage Count',
    ]);

    // Data rows.
    foreach ($data as $item) {
      fputcsv($output, [
        $item['entity_type'],
        $item['bundle'],
        $item['field_name'],
        $item['paragraph_type'],
        $item['reference_count'],
      ]);
    }

    fclose($output);
  }

  /**
   * Output summary statistics.
   *
   * @param array $data
   *   The usage data.
   * @param array $paragraph_types
   *   All paragraph types.
   */
  private function outputSummary(array $data, array $paragraph_types): void {
    $total_paragraphs = count($paragraph_types);
    $used_paragraphs = count(array_unique(array_column($data, 'paragraph_type')));
    $unused_paragraphs = $total_paragraphs - $used_paragraphs;
    $total_usage = array_sum(array_column($data, 'reference_count'));

    $this->output()->writeln('');
    $this->output()->writeln('<info>Summary:</info>');
    $this->output()->writeln(sprintf('Total paragraph types: %d', $total_paragraphs));
    $this->output()->writeln(sprintf('Used paragraph types: %d', $used_paragraphs));
    $this->output()->writeln(sprintf('Unused paragraph types: %d', $unused_paragraphs));
    $this->output()->writeln(sprintf('Total paragraph instances: %d', $total_usage));

    if ($unused_paragraphs > 0) {
      $this->output()->writeln('');
      $this->output()->writeln('<comment>Run with --unused-only to see which paragraph types are not used.</comment>');
    }
  }

}
