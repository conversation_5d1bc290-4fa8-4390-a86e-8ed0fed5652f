<?php

namespace Drupal\luxair_static\EventSubscriber;

use <PERSON><PERSON>al\Core\Url;
use <PERSON><PERSON><PERSON>\tome_static\Event\CollectPathsEvent;
use Dr<PERSON>al\tome_static\Event\TomeStaticEvents;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Event subscriber used for modifying the paths that are collected and later processed from Tome.
 */
final class TomeCollectPathsEventsSubscriber implements EventSubscriberInterface {

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents(): array {
    return [TomeStaticEvents::COLLECT_PATHS => [['collectPaths', -222]]];
  }

  /**
   * Modify the paths that are collected and later processed from Tom<PERSON>.
   *
   * @param \Drupal\tome_static\Event\CollectPathsEvent $event
   *   The collect paths event.
   */
  public function collectPaths(CollectPathsEvent $event) {
    $paths = $event->getPaths();

    // Remove several types of paths.
    foreach ($paths as $k => $path) {
      // Exclude _entity:file:, _entity:ibe_query:, _entity:teaser:.
      if (
        str_contains($path, '_entity:file:')
        || str_contains($path, '_entity:ibe_query:')
        || str_contains($path, '_entity:teaser:')
      ) {
        unset($paths[$k]);
      }

      // Exclude '/file/add' paths from file_entity.routing.yml.
      if (preg_match('!^/(../)?file/add/.+!', $path)) {
        unset($paths[$k]);
      }

      // Exclude entities that are unpublished - only for node, taxonomy_term for now.
      if (preg_match('!^_entity:(node|taxonomy_term):!', $path)) {
        if (!self::isEntityForTomePathPublished($path) ) {
          unset($paths[$k]);
        }
      }
    }

    // Renumber array keys and replace paths. Save ram after that.
    $event->replacePaths($paths = array_values($paths));
    unset($paths);

    // Add manual paths.
    $event->addPaths([
      '/modules/custom/luxair_intelligent_search/app/intelligent-search/dist/intelligent-search/assets/google-icons.svg',
      '/modules/custom/luxair_intelligent_search/app/intelligent-search/dist/intelligent-search/MaterialIcons-Regular.ttf',
      '/modules/custom/luxair_intelligent_search/app/intelligent-search/dist/intelligent-search/MaterialIcons-Regular.woff',
      '/modules/custom/luxair_intelligent_search/app/intelligent-search/dist/intelligent-search/MaterialIcons-Regular.woff2',

      '/modules/custom/luxair_web_contact_form/app/web-contact-form/dist/web-contact-form/assets/google-icons.svg',
      '/modules/custom/luxair_web_contact_form/app/web-contact-form/dist/web-contact-form/assets/luxair-icons.svg',
      '/modules/custom/luxair_web_contact_form/app/web-contact-form/dist/web-contact-form/polyfills-es5.js',

      '/themes/lgit/resources/404-animation-mobile.json',
      '/themes/lgit/resources/404-animation-desktop.json',

      '/sitemap.xml',
      '/robots.txt',

      // All are related to styling the sitemap in the browser.
      '/sitemap_generator/default/sitemap.xsl',
      '/modules/contrib/simple_sitemap/xsl/simple_sitemap.xsl.js',
      '/modules/contrib/simple_sitemap/xsl/simple_sitemap.xsl.css',
      '/modules/contrib/simple_sitemap/xsl/parser-date-iso8601.min.js',
      '/modules/contrib/simple_sitemap/xsl/jquery.tablesorter.min.js',
      '/core/assets/vendor/jquery/jquery.min.js',

      '/luxair_destinations_overview',
    ]);

    if ('luxair' === getenv('DRUPAL_APPLICATION')) {
      foreach (\Drupal::languageManager()->getLanguages() as $language) {
        $event->addPaths([
          "/mobile-app-config/home-offers.json",
          "/{$language->getId()}/mobile-app-config/live/home-offers.json",
          "/{$language->getId()}/mobile-app-config/beta/home-offers.json",
          "/mobile-app-config/marketing-offers.json",
          "/{$language->getId()}/mobile-app-config/live/marketing-offers.json",
          "/{$language->getId()}/mobile-app-config/beta/marketing-offers.json",
          "/mobile-app-config/onboarding-teasers.json",
          "/{$language->getId()}/mobile-app-config/live/onboarding-teasers.json",
          "/{$language->getId()}/mobile-app-config/beta/onboarding-teasers.json",
          "/mobile-app-config/menu-items.json",
          "/{$language->getId()}/mobile-app-config/live/menu-items.json",
          "/{$language->getId()}/mobile-app-config/beta/menu-items.json",
        ]);
      }
    }

    // Add the destinations teaser images paths.
    $destination_images = [];
    $data = \Drupal::service('luxair_destinations_overview.overview_data_service')->getData();
    foreach ($data['data']['destinations'] as $destination) {
      foreach ($destination['field_teaser_image'] ?? [] as $image) {
        if (!empty($image['uri'])) {
          $url = $image['uri'];
          if (filter_var($image['uri'], FILTER_VALIDATE_URL)) {
            $url = '';
            $parsed_url = parse_url($image['uri']);
            if (isset($parsed_url['path'])) {
              $url .= $parsed_url['path'];
            }
            if (isset($parsed_url['query'])) {
              $url .= "?{$parsed_url['query']}";
            }
          }
          $destination_images[] = $url;
        }
      }
    }
    $event->addPaths($destination_images);

    // Add Luxair Pro corporate logo images paths.
    if (!empty(getenv('LUXAIR_PRO_ENABLED'))) {
      $image_style = $this->entityTypeManager
      ->getStorage('image_style')
      ->load('thumbnail');

      $query = $this->database->select('groups', 'g');
      $query->join('groups_field_data', 'gfd', 'g.id = gfd.id and gfd.status = 1');
      $query->leftJoin('group__field_corporate_logo', 'gfcl', 'g.id = gfcl.entity_id');
      $query->leftJoin('file_managed', 'fm', 'gfcl.field_corporate_logo_target_id = fm.fid');
      $query
        ->fields('g', ['id'])
        ->fields('fm', ['uri'])
        ->fields('ufd', ['uid', 'name', 'pass']);
      $corporate_users = array_filter(
        array_map(
          fn($row) => [
            'corporateLogo' => $row['uri']
              ? $image_style->buildUrl($row['uri'])
              : NULL,
          ],
          $query->execute()->fetchAll(\PDO::FETCH_ASSOC)
        ),
        fn($row) => !empty($row['id']),
      );
    }

    foreach (\Drupal::languageManager()->getLanguages() as $language) {
      $event->addPath(
        Url::fromRoute("view.faq_nodes_json.rest_export", [], ['language' => $language])
          ->toString()
      );
    }

    // Get icons and images from luxair_styleguide module.
    $icon_paths = [];
    $luxair_styleguide_path = \Drupal::service('extension.list.module')
      ->getPath('luxair_styleguide');
    $files_fonts = \Drupal::service('file_system')->scanDirectory(
      "{$luxair_styleguide_path}/build/fonts",
      '/.*/',
      ['recurse' => TRUE],
    );
    $files_images = \Drupal::service('file_system')->scanDirectory(
      "{$luxair_styleguide_path}/build/images",
      '/.*/',
      ['recurse' => TRUE],
    );
    foreach (array_merge($files_fonts, $files_images) as $file) {
      $icon_paths[] = '/' . ltrim($file->uri, '/');
    }
    $event->addPaths($icon_paths);

    // For each _entity:node:LANG:NID path, add the /LANG/node/NID path.
    $paths = $event->getPaths();
    foreach ($paths as $k => $path) {
      if (str_contains($path, '_entity:node:')) {
        [, $entity_type, $lang, $entity_id] = explode(':', $path);
        $node_short_llink_path = '/' . implode('/', [$lang, $entity_type, $entity_id]);
        $event->addPath($node_short_llink_path);
      }
    }
    unset($paths);
  }

  /**
   * Check if the $tomePath is of a published entity
   *
   * @param string $path
   *   Entity path starting with '_entity:'.
   *
   * @return bool
   *   If the entity is published.
   */
  private static function isEntityForTomePathPublished(string $path): bool {
    [, $entity_type, $lang, $entity_id] = explode(':', $path);

    // Load and validate the entity.
    return \Drupal::entityTypeManager()
      ->getStorage($entity_type)
      ->load($entity_id)
      ->isPublished();
  }

}
