<?php

namespace Drupal\amadeus_ibe_page\Controller;

use <PERSON>upal\Component\Render\FormattableMarkup;
use <PERSON>upal\Core\Controller\ControllerBase;
use Drupal\Core\Database\Database;
use Drupal\field\Entity\FieldStorageConfig;

/**
 * Returns a placeholder page to embed the IBE.
 */
class AmadeusIBEWaitingPageController extends ControllerBase {

  /**
   *
   */
  public function render() {
    $paragraph_fields = [];
    $field_storages = FieldStorageConfig::loadMultiple();

    foreach ($field_storages as $field_storage) {
      if ($field_storage->getType() === 'entity_reference_revisions' && $field_storage->getSetting('target_type') === 'paragraph') {
        $paragraph_fields[] = $field_storage;
      }
    }

    $connection = Database::getConnection();
    $results = [];

    foreach ($paragraph_fields as $field_storage) {
      $field_name = $field_storage->getName();
      $entity_type = $field_storage->getTargetEntityTypeId();

      $field_instances = \Drupal::entityTypeManager()
        ->getStorage('field_config')
        ->loadByProperties(['field_name' => $field_name]);

      foreach ($field_instances as $instance) {
        $bundle = $instance->getTargetBundle();
        $target_bundles = $instance->getSetting('handler_settings')['target_bundles'] ?? [];

        foreach ($target_bundles as $target_bundle) {
          // Count how many paragraph entities of this type are referenced.
          $table = $entity_type . '__' . $field_name;
          $query = $connection->select($table, 't');
          $query->addExpression('COUNT(*)', 'count');
          $query->condition('t.' . $field_name . '_target_id', NULL, 'IS NOT NULL');
          $query->leftJoin('paragraph__' . $field_name, 'p', 'p.entity_id = t.' . $field_name . '_target_id');
          $query->condition('p.bundle', $target_bundle);

          $count = $query->execute()->fetchField();

          $results[] = [
            'entity_type' => $entity_type,
            'bundle' => $bundle,
            'field_name' => $field_name,
            'paragraph_type' => $target_bundle,
            'reference_count' => $count,
          ];
        }
      }
    }

    foreach ($results as $row) {
      \Drupal::logger('paragraph_analysis')->notice(
      "Entity: {$row['entity_type']} | Bundle: {$row['bundle']} | Field: {$row['field_name']} | Paragraph: {$row['paragraph_type']} | Count: {$row['reference_count']}"
      );
    }

    // $lang = \Drupal::languageManager()->getCurrentLanguage()->getId();
    //     $src = '/'.$lang.'/flight/booking-continue?' . \Drupal::request()->getQueryString();
    //     $markup = <<<EOT
    //     <div class="loading_redirect">
    //         <img src="/modules/custom/luxair_styleguide/build/images/icons/global/lg/loading.gif" alt="loading">
    //         <p>If you are not automatically redirect after 3 seconds please click <a href="$src">here</a></p>
    //     </div>
    //     <script>
    //         setTimeout(function(){
    //           window.location.href = '/' + drupalSettings.path.currentLanguage + '/flight/booking-continue' + window.location.search;
    //         }, 500);
    //     </script>
    //     <style>
    //         .loading_redirect{
    //         position: absolute;
    //         top: 50%;
    //         left: 50%;
    //         transform: translate(-50%, -50%);
    //         display: flex;
    //         align-items: center;
    //         flex-direction: column;
    //         }
    //         img{
    //         transform: rotateY(180deg);
    //         }
    //         .header, .footer{
    //         display: none;
    //         }
    //     </style>
    // EOT;
    return [
      '#cache' => [
        'max-age' => 0,
      ],
      '#type' => 'markup',
      '#markup' => new FormattableMarkup($markup, []),
    ];
  }

}
