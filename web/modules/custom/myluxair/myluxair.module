<?php

/**
 * Implements hook_theme().
 */
function myluxair_theme($existing, $type, $theme, $path)
{
  $themes = [];
  $themes['myluxair_page'] = [
    'variables' => [
      'myluxair_path' => \Drupal::service('extension.list.module')->getPath('myluxair'),
    ]
  ];
  return $themes;
}

/**
 * Implements hook_preprocess_HOOK().
 */
function myluxair_preprocess_myluxair_page(&$variables)
{
  $lang = \Drupal::service('language_manager')->getCurrentLanguage()->getId();
  $link = \Drupal::service('path_alias.manager')->getAliasByPath('/node/107', $lang);

  $entityId = 'amadeus_checkin_url';
  $checkin_url = \Drupal::service('entity_type.manager')->getStorage('luxair_api_endpoint_entity')->load($entityId)->getUri();

  $variables['conditions_link'] = '/' . $lang . $link;
  $variables['checkin_url'] = $checkin_url . '?';
}

/**
 * Include the script used to register the webworker.
 */
function myluxair_page_attachments(array &$page)
{
  // Get the current language.
  $language = \Drupal::languageManager()->getCurrentLanguage()->getId();

  if (strpos(\Drupal::request()->getPathInfo(), 'myluxair') !== false) {
    $page['#attached']['library'][] = 'myluxair/register';
    $page['#attached']['library'][] = "luxair/dxp_assets_myluxair_{$language}";

    // Add base href tag to the HTML head
    $page['#attached']['html_head'][] = [
      [
        '#type' => 'html_tag',
        '#tag' => 'base',
        '#attributes' => [
          'href' => "/{$language}/myluxair",
        ],
      ],
      'myluxair_base_href',
    ];
  }
}

/**
 * Implements hook_preprocess_HOOK().
 * Adds MyLuxair block in the main navigation menu template.
 */
function myluxair_preprocess_block__switches_block(&$variables, $hook)
{
  $variables['content']['myluxair_block_output']['#markup'] = '<div id="feature-auth-management-button"></div>';
}
