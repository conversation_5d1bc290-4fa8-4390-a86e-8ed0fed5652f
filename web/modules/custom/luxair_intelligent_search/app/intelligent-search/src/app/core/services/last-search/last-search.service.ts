import { Inject, Injectable } from '@angular/core';
import { PassengerSelectorModel } from '@luxairgroup/room-picker';
import { Observable, ReplaySubject, Subject, timer } from 'rxjs';
import { map, switchMap, take, takeUntil } from 'rxjs/operators';

import { ANALYTICAL_EVENT_DISPATCH, dispatcher } from 'app/core/analytics/analytics';
import { FLIGHT_SEARCH } from 'app/core/analytics/flight-search';
import { PACKAGE_SEARCH } from 'app/core/analytics/package-search';
import { PassengerHelper, PassengerPickerListHelper } from 'app/shared/helpers/passenger.helper';
import { AemFlightData, SearchData } from 'app/shared/models/aem';
import { FlightHotelForm } from './../../../flight-hotel/flight-hotel.model';
import { FlightFormMCT } from './../../../flight/flight.model';
import { FlightPassengerList } from './../../../flight/passenger-picker/passenger-list.model';
import { Location, LocationType } from './../../../shared/components/form/location-picker/list/location.model';
import { RouteDto } from './../../../shared/dtos/route.dto';
import { Destination } from './../../../shared/models/destination';
import { FlightType } from './../../../shared/models/flightType';
import { LuxairDestinationsService } from './../luxair-destinations.service';
import { LuxairFormsService } from './../luxair-forms.service';
import { LAST_SEARCH_FLIGHT, LAST_SEARCH_PACKAGE, RouteCodes } from './last-search.model';
import { LocalStore, StoreInterface, StubStore } from './stores.service';
import { AirportHelper } from 'app/shared/helpers/airport.helper';
import { ProductType } from 'app/shared/enums/product-type.enum';

declare global {
    interface Window {
        LuxairEventService: {
            getLastSearches: () => Observable<SearchData[]>;
        };
    }
}

@Injectable()
export class LastSearchService {
    private lastSearch: ReplaySubject<FlightFormMCT | FlightHotelForm> = new ReplaySubject(1);
    private readonly formId = 'FLIGHTS_LUXAIR_LU';
    private readonly MAX_AMOUNT_LAST_SEARCH_ENTRIES = 2;
    private availableRoutes: ReplaySubject<RouteDto[]> = new ReplaySubject(1);
    private availableDestinations: ReplaySubject<Destination[]> = new ReplaySubject(1);
    private availableStores: { [key: string]: StoreInterface };
    private stores: { [key: string]: StoreInterface };

    private destroy$ = new Subject<void>();

    private departureLocations: Location[] = [];

    constructor(
        private luxairFormsService: LuxairFormsService,
        private luxairDestinationService: LuxairDestinationsService,
        stub: StubStore,
        local: LocalStore,
        @Inject(ANALYTICAL_EVENT_DISPATCH) private dispatch: dispatcher
    ) {
        this.availableStores = {
            stub,
            LAST_SEARCH_FLIGHT: local,
            LAST_SEARCH_PACKAGE: local,
        };
        this.stores = {
            LAST_SEARCH_FLIGHT: this.availableStores.stub,
            LAST_SEARCH_PACKAGE: this.availableStores.stub,
        };
        // initialisation is always a bit silly
        if (this.getLastSearch(LAST_SEARCH_FLIGHT) === []) {
            this.setLastSearch([], LAST_SEARCH_FLIGHT);
        }
        if (this.getLastSearch(LAST_SEARCH_PACKAGE) === []) {
            this.setLastSearch([], LAST_SEARCH_PACKAGE);
        }

        this.setupLocations();
    }

    private setupLocations() {
        const routesPromise = this.luxairFormsService.getRoutes(this.formId);
        const destinationsPromise = this.luxairDestinationService.getLuxairToursDestinations();

        Promise.all([routesPromise, destinationsPromise]).then(([routes, destinationList]) => {
            this.availableRoutes.next(routes.routes);
            this.departureLocations = AirportHelper.airportToLocation(AirportHelper.getDepartureAirports(routes.routes));
            this.availableDestinations.next(destinationList);
        });
    }


    public getEapLastSearches(): Observable<(FlightFormMCT | FlightHotelForm)[]> {
        return this.waitForDXPScripts().pipe(
            switchMap(() => window.LuxairEventService.getLastSearches()),
            switchMap(lastSearches => {
                const searchPromises = lastSearches
                    .map(async searchData => {
                        let searchParsed;
                        if (searchData.productType === ProductType.FLIGHT) {
                            searchParsed = this.convertToLastSearchFlight(searchData);
                        } else {
                            searchParsed = await this.convertToLastSearchFlightHotel(searchData);
                        }
                        return searchParsed;
                    });
                return Promise.all(searchPromises);
            }),
            map(searches => searches.slice(0, this.MAX_AMOUNT_LAST_SEARCH_ENTRIES))
        );
    }

    private waitForDXPScripts(): Observable<void> {
        return new Observable<void>((observer) => {
            const startTime = Date.now();
            const timeoutMs = 90000; // 1.5 minutes in milliseconds

            const checkLuxairEventService = () => {
                if (window.LuxairEventService) {
                    observer.next();
                    observer.complete();
                    return;
                }

                if (Date.now() - startTime >= timeoutMs) {
                    observer.error(new Error('Timeout waiting for LuxairEventService'));
                    return;
                }
            };

            const subscription = timer(0, 100).pipe(
                takeUntil(timer(timeoutMs)),
                takeUntil(this.destroy$)
            ).subscribe(() => {
                checkLuxairEventService();
            });

            return () => subscription.unsubscribe();
        });
    }

    private updateLastSearchesSaved(lastSearches: SearchData[]) {
        lastSearches.forEach(async (searchData: SearchData) => {
            const searchType = searchData.productType === ProductType.FLIGHT ? LAST_SEARCH_FLIGHT : LAST_SEARCH_PACKAGE;

            const searchParsed =
                searchType === LAST_SEARCH_FLIGHT
                    ? this.convertToLastSearchFlight(searchData)
                    : await this.convertToLastSearchFlightHotel(searchData);

            const lastSearchStoredData = this.prepareLastSearchData(searchParsed as any, searchType).slice(
                0,
                this.MAX_AMOUNT_LAST_SEARCH_ENTRIES
            );
            this.setLastSearch(lastSearchStoredData, searchType);
        });
    }

    public getAvailableDestinations(): Observable<Destination[]> {
        return this.availableDestinations.asObservable();
    }

    public setStoreFlag(key: string, flag: boolean) {
        // this is looking silly. availableStores should be technical, not business feature based
        this.stores[key] = flag ? this.availableStores[key] : this.availableStores.stub;
    }

    private setLastSearch(lastSearchData: (FlightFormMCT | FlightHotelForm)[], key: string): void {
        return this.stores[key].set(key, lastSearchData);
    }

    private prepareLastSearchData(data: FlightFormMCT | FlightHotelForm, key: string) {
        const lastSearchStoredData = this.getLastSearch(key);
        if (!this.isFormAlreadySaved(lastSearchStoredData, data) && !this.isMulticity(data)) {
            lastSearchStoredData.unshift(data);
        }
        return lastSearchStoredData;
    }

    private isMulticity(data: FlightFormMCT | FlightHotelForm): boolean {
        if (isFlightFormMCT(data)) {
            if (data.flightType === FlightType.MultiCity) {
                return true;
            }
        }
        return false;
    }

    public getLastSearch(key: string): (FlightFormMCT | FlightHotelForm)[] {
        const tempData: (FlightFormMCT | FlightHotelForm)[] =
            this.stores[key]?.get(key).slice(0, this.MAX_AMOUNT_LAST_SEARCH_ENTRIES) || [];

        tempData.map(this.updateRouteValidity, this);
        return tempData.map((form) => {
            let departureDate: Date | null = null;
            let returnDate: Date | null = null;
            if (isFlightFormMCT(form)) {
                const segment1 = form.segment1;
                departureDate = segment1.dateRange.departure ? new Date(segment1.dateRange.departure) : null;
                returnDate = segment1.dateRange.return ? new Date(segment1.dateRange.return) : null;
                return {
                    ...form,
                    segment1: {
                        ...segment1,
                        departure: {
                            ...segment1.departure,
                            type: LocationType.LAST_SEARCH,
                        },
                        arrival: {
                            ...segment1.arrival,
                            type: LocationType.LAST_SEARCH,
                        },
                        dateRange: {
                            departure: departureDate,
                            return: returnDate,
                        },
                    },
                };
            } else {
                departureDate = form.dateRange.departure ? new Date(form.dateRange.departure) : null;
                returnDate = form.dateRange.return ? new Date(form.dateRange.return) : null;
                return {
                    ...form,
                    dateRange: {
                        departure: departureDate,
                        return: returnDate,
                    },
                };
            }
        });
    }

    public storeLastSearchFlight(data: FlightFormMCT): void {
        window.LuxairEventService.getLastSearches().pipe(takeUntil(this.destroy$)).subscribe((lastSearches) => {
            const isAlreadySaved = lastSearches.some((lastSearch) => {
                if (lastSearch.productType === ProductType.FLIGHT) {
                    return lastSearch.originCity === data.segment1.departure.code && lastSearch.destinationCity === data.segment1.arrival.code
                        && (new Date(lastSearch.departureTs).toISOString().split('T')[0] === data.segment1.dateRange.departure.toISOString().split('T')[0] && new Date(lastSearch.arrivalTs).toISOString().split('T')[0] === data.segment1.dateRange.return.toISOString().split('T')[0])
                        && lastSearch.adultsNbr === data.passengers.adults && lastSearch.youthsNbr === data.passengers.youths
                        && lastSearch.childrenNbr === data.passengers.children && lastSearch.infantsNbr === data.passengers.infants && lastSearch.tripType === data.flightType;
                }
            });
            if (!isAlreadySaved) {
                const lastSearchStoredData = this.prepareLastSearchData(data, LAST_SEARCH_FLIGHT).slice(0, this.MAX_AMOUNT_LAST_SEARCH_ENTRIES);
                this.storeLastSearch(this.buildFlightEventData(lastSearchStoredData as FlightFormMCT[]), LAST_SEARCH_FLIGHT);
            }
        });
    }


    public storeLastSearchPackage(data: FlightHotelForm): void {
        window.LuxairEventService.getLastSearches().pipe(takeUntil(this.destroy$)).subscribe((lastSearches) => {
            const isAlreadySaved = lastSearches.some((lastSearch) => {
                if (lastSearch.productType === ProductType.PACKAGE) {
                    return lastSearch.originCity === data.departure.code && lastSearch.destinationCity === data.arrival.code
                        && lastSearch.adultsNbr === data.passengerData[0].adultCount && lastSearch.childrenNbr === data.passengerData[0].childCount
                        && new Date(lastSearch.departureTs).toISOString().split('T')[0] === data.dateRange.departure.toISOString().split('T')[0]
                        && new Date(lastSearch.arrivalTs).toISOString().split('T')[0] === data.dateRange.return.toISOString().split('T')[0];
                }
            });
            if (!isAlreadySaved) {
                const lastSearchStoredData = this.prepareLastSearchData(data, LAST_SEARCH_PACKAGE).slice(0, this.MAX_AMOUNT_LAST_SEARCH_ENTRIES);
                this.storeLastSearch(this.buildPackageEventData(lastSearchStoredData as FlightHotelForm[]), LAST_SEARCH_PACKAGE);
            }
        });

    }

    private storeLastSearch(eventData: AemFlightData, lastSearchType: typeof LAST_SEARCH_PACKAGE | typeof LAST_SEARCH_FLIGHT) {
        const luxairEventService = (window as any).LuxairEventService;

        if (luxairEventService) {
            // aep required departureDate to be a string
            if (!eventData.myLuxairSearchData.flightSearch.departureDate) {
                eventData.myLuxairSearchData.flightSearch.departureDate = '2100/01/01'; // temporal solution (departure date is required for aep)
            }
            luxairEventService.sendLastSearch(eventData, lastSearchType).then(() => {
                return luxairEventService.getLastSearches().subscribe((lastSearches) => {
                    this.updateLastSearchesSaved(lastSearches);
                });
            });
        }
    }

    private convertToLastSearchFlight(searchData: SearchData) {
        const departure = this.departureLocations.find((location: Location) => location.code === searchData.originCity);
        const arrival = this.departureLocations.find((location: Location) => location.code === searchData.destinationCity);

        return {
            flightType: searchData.tripType,
            segment1: {
                dateRange: {
                    departure: new Date(searchData.departureTs),
                    return: searchData.arrivalTs ?  new Date(searchData.arrivalTs) : null,
                },
                departure: {
                    label: departure.label,
                    type: 'AIRPORT_DEPARTURE',
                    country: searchData.originCountry,
                    code: searchData.originCity,
                    id: 0,
                },
                arrival: {
                    label: arrival.label,
                    type: 'AIRPORT_ARRIVAL',
                    country: searchData.destinationCountry,
                    code: searchData.destinationCity,
                    id: 1,
                },
            },
            segment2: {
                dateRange: null,
                departure: null,
                arrival: null,
            },
            passengers: {
                adults: searchData.adultsNbr,
                students: 0,
                youths: searchData.youthsNbr,
                children: searchData.childrenNbr,
                infants: searchData.infantsNbr,
            },
            passengersBirthdates: {
                children: [],
            },
        };
    }

    private async convertToLastSearchFlightHotel(searchData: SearchData): Promise<FlightHotelForm> {
        const departure = this.departureLocations.find((location: Location) => location.code === searchData.originCity);

        const availableDestinations = await this.availableDestinations.pipe(take(1)).toPromise();

        const allLGITDestinations = availableDestinations.map((destination) => [destination].concat(destination.groups)).flat();
        const arrival = allLGITDestinations.find((destination) => destination.code === searchData.destinationCity);
        const ageOfChildren = Object.entries(searchData)
            .filter(([key, value]) => key.startsWith('ageOfChild'))
            .map(([key, value]) => value);
        return {
            departure: { label: departure.label, code: searchData.originCity, type: LocationType.LAST_SEARCH, id: 0 },
            arrival: { label: arrival?.name, type: LocationType.DESTINATION, code: searchData.destinationCity, sublocations: null, id: 1 },
            passengerData: [{ adultCount: searchData.adultsNbr, childCount: searchData.childrenNbr, childAges: ageOfChildren }],
            isDateFlexible: searchData.tripType !== 'package_fixed', // override logic to use flexible date
            dateRange: {
                departure: searchData.departureTs ? new Date(searchData.departureTs) : null,
                return: searchData.arrivalTs ? new Date(searchData.arrivalTs) : null
            },
        };
    }

    public getLastSearchData(): Observable<FlightFormMCT | FlightHotelForm> {
        return this.lastSearch.asObservable();
    }

    public updateLastSearchData(data: FlightFormMCT | FlightHotelForm): void {
        this.lastSearch.next(data);
        this.pushUpdateEvent(data);
    }

    public pushUpdateEvent(data: FlightFormMCT | FlightHotelForm): void {
        if (isFlightFormMCT(data)) {
            this.dispatch(FLIGHT_SEARCH.LAST_SEARCH_SELECT, {
                label: data.segment1.departure.code + data.segment1?.arrival?.code,
                value: PassengerPickerListHelper.total(data.passengers),
            });
        } else {
            this.dispatch(PACKAGE_SEARCH.LAST_SEARCH_SELECT, {
                label: data.departure.code + data?.arrival?.code,
                value: PassengerHelper.total(data.passengerData),
            });
        }
    }

    private updateRouteValidity(search: FlightFormMCT | FlightHotelForm): void {
        if (isFlightFormMCT(search)) {
            this.availableRoutes.pipe(take(1)).subscribe((routes) => {
                if (!!routes) {
                    search.closed = !this.isMatchingRoute(routes, {
                        originCode: search.segment1.departure.code,
                        arrivalCode: search.segment1?.arrival?.code,
                    });
                }
            });
        } else {
            this.availableDestinations.pipe(take(1)).subscribe((destinations) => {
                if (!!destinations) {
                    search.closed = !this.isMatchingDestination(destinations, search?.arrival?.code);
                }
            });
        }
    }

    private isMatchingRoute(routes: RouteDto[], { originCode, arrivalCode }: RouteCodes): boolean {
        return routes.some((route) => route.origin.iataCode === originCode && route.destination.iataCode === arrivalCode);
    }

    private isMatchingDestination(destinations: Destination[], arrivalCode: string): boolean {
        return destinations.some((destination) => {
            return destination.code === arrivalCode || destination.groups.some((city) => city.code === arrivalCode);
        });
    }

    private isFormAlreadySaved(lastSearchStoredData: (FlightFormMCT | FlightHotelForm)[], data: FlightFormMCT | FlightHotelForm): boolean {
        return lastSearchStoredData.some((storedDest) => {
            if (isFlightFormMCT(data) && isFlightFormMCT(storedDest)) {
                return this.isFlightFormMCTSaved(storedDest, data);
            } else if (!isFlightFormMCT(data) && !isFlightFormMCT(storedDest)) {
                return this.isFlightHotelFormSaved(storedDest, data);
            }
        });
    }

    private isFlightHotelFormSaved(storedDest: FlightHotelForm, data: FlightHotelForm): boolean {
        return (
            storedDest.departure.code === data.departure.code &&
            storedDest?.arrival?.code === data?.arrival?.code &&
            storedDest.dateRange.departure?.getTime() === data.dateRange.departure?.getTime() &&
            storedDest.dateRange.return?.getTime() === data.dateRange.return?.getTime() &&
            this.isPassengerDataEqual(storedDest, data)
        );
    }

    private isFlightFormMCTSaved(storedDest: FlightFormMCT, data: FlightFormMCT): boolean {
        return (
            storedDest.segment1.departure.code === data.segment1.departure.code &&
            storedDest.segment1?.arrival?.code === data.segment1?.arrival?.code &&
            storedDest.segment1.dateRange.departure.getTime() === data.segment1.dateRange.departure?.getTime() &&
            storedDest.segment1.dateRange.return?.getTime() === data.segment1.dateRange.return?.getTime() &&
            this.isPassengerDataEqual(storedDest, data)
        );
    }

    private isPassengerDataEqual(storedData: FlightFormMCT | FlightHotelForm, newData: FlightFormMCT | FlightHotelForm): boolean {
        if (isFlightFormMCT(storedData) && isFlightFormMCT(newData)) {
            return this.isFlightPassengersEqual(storedData.passengers, newData.passengers);
        } else if (!isFlightFormMCT(storedData) && !isFlightFormMCT(newData)) {
            return this.isPackagePassengersEqual(storedData.passengerData, newData.passengerData);
        }
    }

    private isFlightPassengersEqual(storedData: FlightPassengerList, newData: FlightPassengerList): boolean {
        return (
            storedData.adults === newData.adults &&
            storedData.children === newData.children &&
            storedData.infants === newData.infants &&
            storedData.students === newData.students &&
            storedData.youths === newData.youths
        );
    }

    private isPackagePassengersEqual(storedData: PassengerSelectorModel[], newData: PassengerSelectorModel[]): boolean {
        if (storedData.length !== newData.length) {
            return false;
        }
        for (let index = 0; index < storedData.length; index++) {
            const storedRoom = storedData[index];
            const newRoom = newData[index];
            if (
                storedRoom.adultCount !== newRoom.adultCount ||
                storedRoom.childCount !== newRoom.childCount ||
                storedRoom.childAges !== newRoom.childAges
            ) {
                return false;
            }
        }
        return true;
    }

    // build the event data
    public buildFlightEventData(eventData: FlightFormMCT[]) {
        let buildData: AemFlightData;

        if (eventData && eventData.length > 0) {
            const data = eventData[0];
            buildData = {
                myLuxairSearchData: {
                    flightSearch: {
                        departureDate: data.segment1.dateRange?.departure?.toISOString() ?? '',
                        destinationCity: data.segment1?.arrival?.code,
                        destinationCityLabel: data.segment1?.arrival?.label,
                        destinationCountry: data.segment1?.arrival?.country,
                        passengerComposition: {
                            adults: data.passengers.adults,
                            children: data.passengers.children,
                            infants: data.passengers.infants,
                            youth: data.passengers.youths,
                        },
                        returnDate: data.segment1.dateRange?.return?.toISOString() ?? '',
                        sourceCity: data.segment1.departure.code,
                        sourceCityLabel: data.segment1.departure.label,
                        sourceCountry: data.segment1.departure.country,
                        tripType: data.flightType,
                        roomsNbr: 1
                    },
                },
            };
        }

        return buildData;
    }

    private buildPackageEventData(eventData: FlightHotelForm[]) {
        let buildData: AemFlightData;

        if (eventData && eventData.length > 0) {
            const data = eventData[0];

            buildData = {
                myLuxairSearchData: {
                    flightSearch: {
                        departureDate: data.dateRange?.departure?.toISOString() ?? '2100/01/01', // temporal solution (departure date is required for aep)
                        destinationCity: data?.arrival?.code,
                        destinationCityLabel: data?.arrival?.label,
                        destinationCountry: data?.arrival?.country,
                        passengerComposition: {
                            adults: data.passengerData[0].adultCount,
                            children: data.passengerData[0].childCount,
                            infants: 0,
                            youth: 0,
                            childAges: data.passengerData[0].childAges,
                        },
                        returnDate: data.dateRange?.return?.toISOString() ?? '2100/01/01', // temporal solution (departure date is required for aep)
                        sourceCity: data.departure.code,
                        sourceCityLabel: data.departure.label,
                        sourceCountry: data.departure.country,
                        tripType: data.isDateFlexible ? 'package_flexible' : 'package_fixed', // this field will be used for flexible date
                        roomsNbr: 1
                    },
                },
            };

            if (data.passengerData.length > 1) {
                buildData = {
                    ...buildData,
                    myLuxairSearchData: {
                        ...buildData.myLuxairSearchData,
                        flightSearch: {
                            ...buildData.myLuxairSearchData.flightSearch,
                            passengerComposition: {
                                ...buildData.myLuxairSearchData.flightSearch.passengerComposition,
                                rooms: data.passengerData.length,
                            }
                        }
                    }
                };
            }
        }
        return buildData;
    }
}

export function isFlightFormMCT(form: FlightFormMCT | FlightHotelForm): form is FlightFormMCT {
    return (form as FlightFormMCT).passengers !== undefined;
}
