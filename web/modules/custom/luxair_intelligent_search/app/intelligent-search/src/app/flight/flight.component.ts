import { Component, ElementRef, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, QueryList, ViewChildren } from '@angular/core';
import { AbstractControl, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';

import { cloneDeep, merge } from 'lodash-es';
import { async, BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, skipUntil, takeUntil } from 'rxjs/operators';

import {
    DRUPAL_SETTINGS,
    DrupalSettings,
    FlightSearchService,
    isFlightFormMCT,
    LastSearchService,
    LuxairFormsService,
    WINDOW
} from '@core';
import {
    Airport,
    AirportHelper,
    DateRange,
    DialogComponent,
    FlightDto,
    FlightType,
    HtmlElementHelper,
    Location,
    LocationType,
    PassengerPickerList,
    PassengerPickerListHelper,
    RouteDto,
    RoutesDto,
    ScrollHelper,
    TabIndexHelper,
    TabService,
    TransPipe,
    TypedControl,
    TypedFormBuilder,
    TypedGroup,

} from '@shared';
import { ANALYTICAL_EVENT_DISPATCH, dispatcher } from 'app/core/analytics/analytics';
import { FLIGHT_SEARCH } from 'app/core/analytics/flight-search';
import { PageComponent } from '../page.component';
import { LAST_SEARCH_FLIGHT } from './../core/services/last-search/last-search.model';
import { FlightDatepickerComponent } from './datepicker/datepicker.component';
import { destinationUrls } from './destination-urls.const';
import {
    DatePickerConfiguration,
    DatePickerFlightFormProjection,
    FlightFormMCT,
    FlightSegment,
    projectFlightFormForDatepicker,
} from './flight.model';
import { requiredDistinctRoute, requiredOnMulticity, requiredPrecedence } from './multicity-validators';
import { FlightPassengerBirthdateList, FlightPassengerList } from './passenger-picker/passenger-list.model';
import { LastSearchDestination } from './last-search.model';
import { FlightHotelForm } from 'app/flight-hotel/flight-hotel.model';
import { ValidatorHelper } from 'app/shared/helpers/validator.helper';
import { ProductType } from './enums/product-type.enum';
import { BookingSearchJsonBuilder } from 'app/core/services/search/flight/booking-search-refx.service';
import { CookiesService } from '../core/services/cookies.service';


type segmentKey = 'segment1' | 'segment2';

@Component({
    selector: 'is-intelligent-flight-search',
    templateUrl: './flight.component.html',
    styleUrls: ['./flight.component.scss'],
})
export class FlightComponent extends PageComponent implements OnInit, OnDestroy {


    flightDto: FlightDto;
    formGroup: TypedGroup<FlightFormMCT>;
    routesDto: RoutesDto;

    departuresLocation: Location[] = [];


    arrivalsLocation /*: dict<segmentKey, Location>*/ = {
        segment1: [],
        segment2: [],
    };

    passengers: FlightPassengerList = {
        adults: 1,
        students: 0,
        youths: 0,
        children: 0,
        infants: 0,
    };

    passengersBirthdates: FlightPassengerBirthdateList = {
        children: [],
    };

    @ViewChildren(FlightDatepickerComponent) flightDatepickerComponents: QueryList<FlightDatepickerComponent>;

    get datepickers(): { [key: string]: FlightDatepickerComponent } {
        const pickers = {};
        this.flightDatepickerComponents?.toArray().map((picker, key) => {
            pickers['segment' + (key + 1)] = picker;
        });
        return pickers;
    }

    get anyCalendarLoading() {
        return Object.values(this.datepickers).some((picker) => picker.isDatesAvailabilityLoading);
    }

    private areCalendarsInvalid = new BehaviorSubject<boolean>(false);

    get anyCalendarInvalid$() {
        // We need to add the timeout as the template is updated before the value is not up to date yet
        setTimeout(() => this.areCalendarsInvalid.next(Object.values(this.datepickers).some((picker) => picker.isInvalid)));
        return this.areCalendarsInvalid;
    }

    // used for umnr. [allowsUmnr] input used concurrently
    get currentDateRange() {
        return this.formGroup.get('segment1').value.dateRange;
    }

    datepickerConfiguration: DatePickerConfiguration = {
        histogram: {
            duration: 7,
            month: null,
        },
        isCalendarHidden: false,
        isHistogramHidden: false,
        showPromo: false,
        openOverlayByDefault: false,
        activeDatepickerTabIndex: 0,
        minDate: undefined,
        maxDate: undefined,
        onlyPromo: false,
    };

    adultsIncrement = 1;
    disableAdults = false;
    disableStudents = false;
    disableYouths = false;
    disableChildren = false;
    disableInfants = false;
    showStudentMessage = false;
    enabledLocation = false;

    studentsCount = 0;
    youthsCount = 0;

    flightTypes: Array<any> /* FlightType[] */ = [];
    defaultFlightType: FlightType;
    flightType = FlightType.RoundTrip;
    airport: Airport;

    get currentFlightType() {
        return this.formGroup.get('flightType').value;
    }

    get isMultiCity() {
        return this.currentFlightType === FlightType.MultiCity;
    }

    get passengersCount(): string {
        return '' + PassengerPickerListHelper.total(this.formGroup.value.passengers);
    }

    skipUntilIsLoaded = new Subject<boolean>();

    public acceptsAnywhere = true;
    public isAnywhereDestinationSelected = false;
    public submitting = false;
    public locationType = LAST_SEARCH_FLIGHT;

    private readonly destroy$ = new Subject<void>();
    perfDebounce = 125;


    private currentMappedLastSearchData: FlightFormMCT | FlightHotelForm;

    public refxUrl = this._settings.luxair_api_endpoint.LUXAIR_REFX_URL;
    public refxJsonPayload = '';
    private refxCookies = 'REFX_ENABLED';


    constructor(
        private elementRef: ElementRef,
        private typedFormBuilder: TypedFormBuilder,
        private flightSearchService: FlightSearchService,
        private luxairFormsService: LuxairFormsService,
        private dialog: MatDialog,
        private transPipe: TransPipe,
        @Inject(WINDOW) private window: Window,
        @Inject(ANALYTICAL_EVENT_DISPATCH) private dispatch: dispatcher,
        public lastSearchService: LastSearchService,
        private tabService: TabService,
        @Inject(DRUPAL_SETTINGS) private _settings: DrupalSettings,
        private readonly cookiesService: CookiesService,
    ) {
        super(window);

    }


    async ngOnInit() {
        const latestLastSearchListener = (event: CustomEvent) => {
            this.onLastSearchCardClick(event.detail);
        };
        this.window.addEventListener('searchDataEvent', latestLastSearchListener);


        this.flightDto = HtmlElementHelper.getTypedHtmlAttributesList(this.elementRef.nativeElement, FlightDto);
        this.flightTypes = this.flightDto.flightTypes.split(',') as FlightType[];
        this.flightType = this.flightDto.defaultFlightType as FlightType;
        this.acceptsAnywhere = !!this.flightDto.isAnywhereAllowed;
        this.formGroup = this.typedFormBuilder.group<FlightFormMCT>({
            passengers: [null],
            passengersBirthdates: [null],
            flightType: [this.flightType],
            // among the 8 possible segment we are limiting ourselves to 2, let's keep it _simple_
            segment1: this.typedFormBuilder.group<FlightSegment>({
                dateRange: [null, [Validators.required]],
                departure: [null, [Validators.required]],
                arrival: [null, [Validators.required]],
            }),
            segment2: this.typedFormBuilder.group<FlightSegment>({
                dateRange: null,
                departure: null,
                arrival: null,
            }),
        });
        this.formGroup
            .get('segment1')
            .get('departure')
            .setValidators([Validators.required, requiredDistinctRoute(this.formGroup)]);
        this.formGroup
            .get('segment1')
            .get('arrival')
            .setValidators([Validators.required, requiredDistinctRoute(this.formGroup)]);

        this.formGroup
            .get('segment1')
            .get('dateRange')
            .setValidators([
                Validators.required,
                requiredPrecedence(this.formGroup, () => this.datepickers?.segment1?.dateRangeGroup.get('dateRange'),
                ),
            ]);
        // multicity extra validation : distinct routes, segment dates precedence.
        if (this.flightTypes.includes(FlightType.MultiCity)) {
            this.formGroup
                .get('segment2')
                .get('departure')
                .setValidators([requiredOnMulticity(this.formGroup), requiredDistinctRoute(this.formGroup)]);
            this.formGroup
                .get('segment2')
                .get('arrival')
                .setValidators([requiredOnMulticity(this.formGroup), requiredDistinctRoute(this.formGroup)]);
            this.formGroup
                .get('segment2')
                .get('dateRange')
                .setValidators([
                    requiredOnMulticity(this.formGroup),
                    requiredPrecedence(this.formGroup, () => this.datepickers?.segment2?.dateRangeGroup.get('dateRange')),
                ]);

        }

        await this.populate();

        combineLatest([this.formGroup.get('flightType').valueChanges, this.flightDatepickerComponents.changes])
            .pipe(
                skipUntil(this.skipUntilIsLoaded),
                takeUntil(this.destroy$),
                debounceTime(this.perfDebounce),
                filter(([flightType]) => !!flightType),
                distinctUntilChanged(([prev], [curr]) => prev !== curr)
            )
            .subscribe(([flightType, pickers]) => {

                if (
                    !this.submitting &&
                    !(
                        flightType === FlightType.MultiCity &&
                        this.formGroup.get('segment1').get('arrival').value?.type === LocationType.ANYWHERE
                    )
                ) {
                    const picker = pickers.last;
                    if (!!picker) {
                        this.formGroup.get('segment2').setValue({
                            dateRange: null,
                            departure: null,
                            arrival: null,
                        });
                        const segment2 = this.formGroup.get('segment2').value;
                        try {
                            const pendingSegment = this.createPendingProjection(this.formGroup.value, { segment2 }, 'segment2');
                            if (pendingSegment) {
                                this.updateBypassCalendarCheck(pendingSegment, picker);
                                this.updateCalendar(pendingSegment, picker);
                            }
                        } catch (someTypeError) {
                        }
                    }
                }

            });

        this.formGroup.get('segment1').valueChanges.subscribe(() => {
            this.flightSearchService.setSelectedArrivals([
                this.formGroup.get('segment1')?.get('arrival')?.value?.code,
                this.formGroup.get('segment1')?.get('departure')?.value?.code,
                this.formGroup.get('segment2')?.get('arrival')?.value?.code,
                this.formGroup.get('segment2')?.get('departure')?.value?.code
            ]);
        });

        this.formGroup.get('segment2').valueChanges.subscribe(() => {
            this.flightSearchService.setSelectedArrivals([
                this.formGroup.get('segment2')?.get('arrival')?.value?.code,
                this.formGroup.get('segment2')?.get('departure')?.value?.code,
                this.formGroup.get('segment1')?.get('arrival')?.value?.code,
                this.formGroup.get('segment1')?.get('departure')?.value?.code
            ]);
        });

        // reset the Anywhere if flightType change
        this.formGroup
            .get('flightType')
            .valueChanges.pipe(
                skipUntil(this.skipUntilIsLoaded),
                takeUntil(this.destroy$),
                debounceTime(this.perfDebounce)
                // distinctUntilChanged prevents this from functioning at all for reasons.
                // some changes will be dropped.
            )
            .subscribe((flightType: FlightType) => {
                // refresh arrival date because round trip and one way trip can have differents routes
                const departure = this.formGroup.get('segment1').get('departure').value;
                const arrivalControl = this.formGroup.get('segment1').get('arrival');
                this.updateArrival(departure, arrivalControl, 'segment1');

                if (
                    flightType === FlightType.MultiCity &&
                    this.formGroup.get('segment1').get('arrival').value?.type === LocationType.ANYWHERE
                ) {
                    this.formGroup.get('segment1').get('arrival').setValue(null);
                    this.resetAnywhere(this.formGroup.get('segment1'));
                }

                // update the next segment to avoid false-negative invalidity
                this.formGroup.get('segment1').get('departure').updateValueAndValidity();
                this.formGroup.get('segment1').get('arrival').updateValueAndValidity();

                // force update on segment2, especially when switching from MultiCity
                this.formGroup.get('segment2').get('departure').updateValueAndValidity();
                this.formGroup.get('segment2').get('arrival').updateValueAndValidity();
                this.formGroup.get('segment2').get('dateRange').updateValueAndValidity();
            });

        this.formGroup
            .get('segment1')
            .get('departure')
            .valueChanges.pipe(
                skipUntil(this.skipUntilIsLoaded),
                takeUntil(this.destroy$),
                debounceTime(this.perfDebounce),
                filter((departure) => !!departure),
                distinctUntilChanged((prev, curr) => this.isSameLocation(prev, curr))
            )
            .subscribe((departure: Location) => {
                const picker = this.datepickers.segment1;
                const arrivalControl = this.formGroup.get('segment1').get('arrival');
                this.updateArrival(departure, arrivalControl, 'segment1');
                if (arrivalControl.value && !this.isAnywhereDestinationSelected) {
                    try {
                        const pendingSegment = this.createPendingProjection(this.formGroup.value, { segment1: { departure } }, 'segment1');
                        if (pendingSegment) {
                            const route = this.updateBypassCalendarCheck(pendingSegment, picker);
                            this.updateCalendar(pendingSegment, picker);
                            this.updateHistogram(pendingSegment, picker, route);
                        }
                    } catch (someTypeError) {
                    }
                }
                // update the next segment to avoid false-negative invalidity
                this.formGroup.get('segment2').get('departure').updateValueAndValidity();
                this.formGroup.get('segment2').get('arrival').updateValueAndValidity();
            });
        if (this.flightTypes.includes(FlightType.MultiCity)) {
            this.formGroup
                .get('segment2')
                .get('departure')
                .valueChanges.pipe(
                    skipUntil(this.skipUntilIsLoaded),
                    takeUntil(this.destroy$),
                    debounceTime(this.perfDebounce),
                    filter(() => this.formGroup.get('flightType').value === FlightType.MultiCity),
                    distinctUntilChanged((prev, curr) => this.isSameLocation(prev, curr))
                )
                .subscribe((departure: Location) => {

                    const picker = this.datepickers.segment2;
                    const arrivalControl = this.formGroup.get('segment2').get('arrival');
                    if (!!departure) {
                        this.updateArrival(departure, arrivalControl, 'segment2');
                    }
                    if (arrivalControl.value && !this.isAnywhereDestinationSelected) {
                        try {
                            const pendingSegment = this.createPendingProjection(
                                this.formGroup.value,
                                { segment2: { departure } },
                                'segment2'
                            );
                            if (pendingSegment) {
                                const route = this.updateBypassCalendarCheck(pendingSegment, picker);
                                this.updateCalendar(pendingSegment, picker);
                                this.updateHistogram(pendingSegment, picker, route);
                            }
                        } catch (someTypeError) {
                        }
                    }
                    // update the next segment to avoid false-negative invalidity
                    this.formGroup.get('segment1').get('departure').updateValueAndValidity();
                    this.formGroup.get('segment1').get('arrival').updateValueAndValidity();
                });
        }
        if (this.flightDto.isLastSearchAllowed) {
            this.lastSearchService
                .getLastSearchData()
                .pipe(takeUntil(this.destroy$), debounceTime(this.perfDebounce))
                .subscribe((form: FlightFormMCT) => {
                    if (isFlightFormMCT(form)) {
                        this.formGroup.get('flightType').setValue(form.flightType);
                        this.formGroup.get('segment1').get('departure').setValue(form.segment1.departure);
                        this.formGroup.get('segment1').get('arrival').setValue(form.segment1.arrival);
                        if (form.segment1.dateRange.departure.getTime() - new Date().getTime() < 0) {
                            setTimeout(() => {
                                const newDatePickerConfig = { ...this.datepickerConfiguration, openOverlayByDefault: false };
                                this.datepickerConfiguration = newDatePickerConfig;
                            });
                            setTimeout(() => {
                                const newDatePickerConfig = { ...this.datepickerConfiguration, openOverlayByDefault: true };
                                this.datepickerConfiguration = newDatePickerConfig;
                            });
                        }
                    }
                });
        }

        this.formGroup
            .get('segment1')
            .get('arrival')
            .valueChanges.pipe(
                skipUntil(this.skipUntilIsLoaded),
                // on setting the DEPARTURE field both values are actually Boolean(true) ...
                takeUntil(this.destroy$),
                debounceTime(this.perfDebounce),
                distinctUntilChanged((prev, curr) => prev === curr || this.isSameLocation(prev, curr))
            )
            .subscribe((arrival: Location) => {
                if (!!arrival) {
                    if (arrival.type === LocationType.ANYWHERE) {
                        if (this.flightType !== FlightType.MultiCity) {
                            this.isAnywhereDestinationSelected = true;
                            this.formGroup.get('segment1').get('dateRange').clearValidators();
                            this.formGroup.get('segment1').get('dateRange').updateValueAndValidity();
                        } else {
                            this.resetAnywhere(this.formGroup.get('segment1'));
                            this.formGroup.get('segment1').get('arrival').setValue(null);
                        }
                    } else {
                        this.resetAnywhere(this.formGroup.get('segment1'));

                        const pendingSegment = this.createPendingProjection(this.formGroup.value, { segment1: { arrival } }, 'segment1');
                        const picker = this.datepickers.segment1;
                        if (arrival.code && pendingSegment) {
                            const route = this.updateBypassCalendarCheck(pendingSegment, picker);
                            this.updateCalendar(pendingSegment, picker);
                            this.updateHistogram(pendingSegment, picker, route);
                        }
                    }

                    // update the next segment to avoid false-negative invalidity
                    this.formGroup.get('segment2').get('departure').updateValueAndValidity();
                    this.formGroup.get('segment2').get('arrival').updateValueAndValidity();
                    this.formGroup.get('segment1').get('departure').updateValueAndValidity();


                }
            });
        if (this.flightTypes.includes(FlightType.MultiCity)) {
            this.formGroup
                .get('segment2')
                .get('arrival')
                .valueChanges.pipe(
                    skipUntil(this.skipUntilIsLoaded),
                    takeUntil(this.destroy$),
                    debounceTime(this.perfDebounce),
                    filter(() => this.formGroup.get('flightType').value === FlightType.MultiCity),
                    distinctUntilChanged((prev, curr) => this.isSameLocation(prev, curr))
                )
                .subscribe((arrival: Location) => {
                    try {
                        const pendingSegment = this.createPendingProjection(this.formGroup.value, { segment2: { arrival } }, 'segment2');
                        const picker = this.datepickers.segment2;
                        if (arrival.code && pendingSegment) {
                            const route = this.updateBypassCalendarCheck(pendingSegment, picker);
                            this.updateCalendar(pendingSegment, picker);
                            this.updateHistogram(pendingSegment, picker, route);
                        }
                    } catch (someTypeError) {
                    }
                    // update the next segment to avoid false-negative invalidity
                    this.formGroup.get('segment1').get('departure').updateValueAndValidity();
                    this.formGroup.get('segment1').get('arrival').updateValueAndValidity();
                    this.formGroup.get('segment2').get('departure').updateValueAndValidity();
                });
        }

        this.formGroup
            .get('passengers')
            .valueChanges.pipe(
                skipUntil(this.skipUntilIsLoaded),
                takeUntil(this.destroy$),
                debounceTime(this.perfDebounce),
                filter((passengers) => !!passengers),
                distinctUntilChanged((prev, curr) => PassengerPickerListHelper.equals(prev, curr))
            )
            .subscribe((passengers: PassengerPickerList) => {
                Object.entries(this.datepickers).forEach((datepickerRef: [segmentKey, FlightDatepickerComponent]) => {
                    try {
                        const pendingSegment = this.createPendingProjection(this.formGroup.value, { passengers }, datepickerRef[0]);
                        if (pendingSegment) {
                            this.updateBypassCalendarCheck(pendingSegment, datepickerRef[1]);
                            this.updateCalendar(pendingSegment, datepickerRef[1]);
                        }
                    } catch (someTypeError) {
                    }
                });
            });
    }


    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }


    createPendingProjection(prevForm: FlightFormMCT, pendingChange: any, segment: segmentKey = 'segment1'): DatePickerFlightFormProjection {
        const pendingForm = merge(cloneDeep(prevForm), pendingChange);
        try {
            return projectFlightFormForDatepicker(pendingForm, segment);
        } catch (t) {
            // TypeError because too early
            return null;
        }
    }

    updateBypassCalendarCheck(projection: DatePickerFlightFormProjection, picker: FlightDatepickerComponent): RouteDto | null {
        const route = this.routesDto.routes.find(
            (r) => r.origin.iataCode === projection.departureIata && r.destination.iataCode === projection.arrivalIata
        );
        picker.bypassCalendarCheck = route?.bypassCalendarCheck === 1;
        return route;
    }

    updateCalendar(projection: DatePickerFlightFormProjection, picker: FlightDatepickerComponent) {
        picker.onDateAvailabilityOutdated(projection, this.flightDto.calendarFilterValue);
    }

    updateHistogram(projection: DatePickerFlightFormProjection, picker: FlightDatepickerComponent, route: RouteDto | null) {
        if (!!route) {
            picker.updateMonthPrices(picker.histogram, projection);
        }
    }

    resetAnywhere(segment: TypedControl<FlightSegment>) {
        if (this.isAnywhereDestinationSelected) {
            this.isAnywhereDestinationSelected = false;
            segment.get('arrival').setValue(null);
            segment.get('dateRange').setValidators(this.flightDatepickerComponents.first.validators);
            segment.get('dateRange').updateValueAndValidity();
        }
    }

    updateArrival(departure: Location, arrivalControl: AbstractControl, segment: segmentKey = 'segment1') {
        this.arrivalsLocation[segment] = AirportHelper.airportToLocation(
            AirportHelper.getArrivalAirports(this.routesDto.routes, departure.code, this.currentFlightType),
            LocationType.AIRPORT_ARRIVAL
        );
        // conditional reset
        if (
            arrivalControl.value &&
            arrivalControl.value.type !== LocationType.ANYWHERE &&
            (departure.code === arrivalControl.value.code ||
                !this.arrivalsLocation[segment].find((l) => l.code === arrivalControl.value.code))
        ) {
            if (!this.currentMappedLastSearchData) {
                arrivalControl.setValue(null);
                this.datepickers[segment].reset();
            } else {
                arrivalControl.markAllAsTouched();
                arrivalControl.updateValueAndValidity();


            }
        }
    }

    // using js function instead of router module because it has been removed from the project
    public overideParameter(): void {
        const searchParams = new URLSearchParams(location.search);
        const arrival = searchParams.get('ret');
        const departure = searchParams.get('dep');
        if (arrival) {
            this.flightDto.defaultArrival = arrival;
        }

        if (departure) {
            this.flightDto.defaultDeparture = departure;
        }
    }

    async populate() {
        this.flightDto.routesFormId = this.flightDto.routesFormId || 'FLIGHTS_LUXAIR_LU';

        this.datepickerConfiguration = {
            histogram: {
                duration: this.flightDto.defaultTripDuration || 7,
                month: null,
            },
            isCalendarHidden: false || this.flightDto.disableCalendar === 1,
            isHistogramHidden: false || this.flightDto.disableHistogram === 1,
            showPromo: false || this.flightDto.promo === 1,
            openOverlayByDefault: false || this.flightDto.datepickerInitiallyOpen === 1,
            activeDatepickerTabIndex: 0 || Number(this.flightDto.intelligentDefaultTab), // otherwise nullish
            minDate: this.flightDto.startDateVoucher ? new Date(this.flightDto.startDateVoucher) : undefined,
            maxDate: this.flightDto.endDateVoucher ? new Date(this.flightDto.endDateVoucher) : undefined,
            onlyPromo: false || !!this.flightDto.onlyPromo,
        };

        if (this.flightDto.adultsIncrement) {
            this.adultsIncrement = this.flightDto.adultsIncrement;
        }

        if (this.flightDto.disableAdults) {
            this.disableAdults = this.flightDto.disableAdults === 1;
        }

        if (this.flightDto.disableStudents) {
            this.disableStudents = this.flightDto.disableStudents === 1;
        }

        if (this.flightDto.studentsCount) {
            this.studentsCount = this.flightDto.studentsCount;
        }

        if (this.flightDto.youthsCount) {
            this.youthsCount = this.flightDto.youthsCount;
        }

        if (this.flightDto.disableYouths) {
            this.disableYouths = this.flightDto.disableYouths === 1;
        }

        if (this.flightDto.disableChildren) {
            this.disableChildren = this.flightDto.disableChildren === 1;
        }

        if (this.flightDto.disableInfants) {
            this.disableInfants = this.flightDto.disableInfants === 1;
        }

        if (this.flightDto.showStudentMessage) {
            this.showStudentMessage = this.flightDto.showStudentMessage === 1;
        }

        if (this.flightDto.enabledLocation) {
            this.enabledLocation = this.flightDto.enabledLocation === 1;
        }

        this.passengers.adults = this.disableAdults ? 0 : this.adultsIncrement ? this.adultsIncrement : this.passengers.adults;
        this.passengers.students = this.disableStudents ? 0 : this.passengers.students ? this.passengers.students : this.studentsCount;
        this.passengers.youths = this.disableYouths ? 0 : this.passengers.youths ? this.passengers.youths : this.youthsCount;
        this.passengers.children = this.disableChildren ? 0 : this.passengers.children;
        this.passengers.infants = this.disableInfants ? 0 : this.passengers.infants;

        this.lastSearchService.setStoreFlag(LAST_SEARCH_FLIGHT, Boolean(this.flightDto.isLastSearchAllowed));

        this.skipUntilIsLoaded.pipe(takeUntil(this.destroy$), debounceTime(this.perfDebounce)).subscribe(async () => {
            this.overideParameter();
            this.routesDto = await this.luxairFormsService.getRoutes(this.flightDto.routesFormId);

            this.departuresLocation = AirportHelper.airportToLocation(AirportHelper.getDepartureAirports(this.routesDto.routes));
            this.arrivalsLocation.segment1 = AirportHelper.airportToLocation(
                AirportHelper.getArrivalAirports(this.routesDto.routes, this.flightDto.defaultDeparture),
                LocationType.AIRPORT_ARRIVAL
            );
            if (this.flightDto.defaultDeparture) {
                if (this.enabledLocation) {
                    AirportHelper.getNearestLocationOfUser(this.routesDto.routes).then((code: string) => {
                        if (!!code) {
                            this.formGroup
                                .get('segment1')
                                .get('departure')
                                .setValue(this.departuresLocation.find((location) => location.code === code));
                        } else {
                            this.formGroup
                                .get('segment1')
                                .get('departure')
                                .setValue(this.departuresLocation.find((location) => location.code === this.flightDto.defaultDeparture));
                        }
                    });
                } else {
                    this.formGroup
                        .get('segment1')
                        .get('departure')
                        .setValue(this.departuresLocation.find((location) => location.code === this.flightDto.defaultDeparture));
                }
            }

            if (this.flightDto.defaultArrival) {
                this.formGroup
                    .get('segment1')
                    .get('arrival')
                    .setValue(this.arrivalsLocation.segment1.find((location) => location.code === this.flightDto.defaultArrival));
            }

            this.updateControlsStatus();
        });
    }


    private updateControlsStatus() {
        if (this.flightDto.defaultDeparture) {
            this.formGroup.get('segment1').get('departure').markAllAsTouched();
        }
        if (this.flightDto.studentsCount) {
            this.formGroup.get('passengers').markAllAsTouched();
        }
        if (this.flightDto.defaultArrival) {
            this.formGroup.get('segment1').get('arrival').markAllAsTouched();
        }

        this.formGroup.updateValueAndValidity();
    }

    getActiveDatepickerTabIndex(segment: segmentKey = 'segment1'): number {
        return (this.formGroup.get(segment).get('dateRange').untouched && this.isUMNR()) ||
            this.formGroup.get(segment).get('arrival').value?.type === LocationType.LAST_SEARCH
            ? 0
            : this.datepickerConfiguration.activeDatepickerTabIndex;
    }

    clickOnDatepicker(event: MouseEvent, segment: segmentKey = 'segment1') {
        this.formGroup.get(segment).get('departure').markAllAsTouched();
        this.formGroup.get(segment).get('arrival').markAllAsTouched();
        this.formGroup.updateValueAndValidity();
    }

    onDateRangeChange(dateRange: DateRange, segment: segmentKey = 'segment1') {
        this.formGroup.get(segment).patchValue({ dateRange });
    }

    projectSegment(segment: segmentKey = 'segment1') {
        try {
            return projectFlightFormForDatepicker(this.formGroup.value, segment);
        } catch (t) {
            // TypeError because too early
            return null;
        }
    }

    onSubmit(noLastSearch: boolean = false) {
        if (this.isAnywhereDestinationSelected) {
            let language = window.location.pathname.split('/')[1];
            if (!language) {
                language = 'en';
            }
            this.dispatch(FLIGHT_SEARCH.SUBMIT_ANYWHERE);

            const url = `/${language}/${destinationUrls[language]}`;

            if (this.isLoadedInReactNative) {
                this.postUrl(url);
            } else {
                window.location.href = url;
            }
        } else {
            // note : event not supported for multicity
            this.dispatch(FLIGHT_SEARCH.SUBMIT_TO, { label: this.formGroup.value.segment1.arrival.code });
            if (this.isUMNR()) {
                this.dialog.open(DialogComponent, {
                    panelClass: 'is-bootstrap',
                    minHeight: '250px',
                    maxHeight: '450px',
                    maxWidth: '700px',
                    data: {
                        imageSrc: this.transPipe.transform('i18n::intelligent-search-flight.umnr-image-link'),
                        imageAlt: this.transPipe.transform('i18n::intelligent-search-flight.umnr-image-alt'),
                        imageMinWidth: '250px',
                        imageMinHeight: '125px',
                        title: this.transPipe.transform('i18n::intelligent-search-flight.umnr-title'),
                        text: this.transPipe.transform('i18n::intelligent-search-flight.umnr-text'),
                        link: this.transPipe.transform('i18n::intelligent-search-flight.umnr-more-info-link'),
                        linkLabel: this.transPipe.transform('i18n::intelligent-search-flight.umnr-more-info-label'),
                        cancelLabel: this.transPipe.transform('i18n::intelligent-search-flight.umnr-cancel-label'),
                        confirmLabel: this.transPipe.transform('i18n::intelligent-search-flight.umnr-confirm-label'),
                        confirmCallback: () => {
                            this.lastSearchService.storeLastSearchFlight(this.formGroup.value);

                            const url = this.flightSearchService.for(this.formGroup.value, this.flightDto).build();
                            if (this.isLoadedInReactNative) {
                                this.postUrl(url);
                            } else {
                                this.window.open(url);
                            }
                            this.submitting = false;
                        },
                    },
                });
            } else if (this.isStudents()) {
                this.dialog.open(DialogComponent, {
                    panelClass: 'is-bootstrap',
                    minHeight: '250px',
                    maxHeight: '450px',
                    maxWidth: '700px',
                    data: {
                        imageSrc: this.transPipe.transform('i18n::intelligent-search-flight.students-image-link'),
                        imageAlt: this.transPipe.transform('i18n::intelligent-search-flight.students-image-alt'),
                        imageMinWidth: '250px',
                        imageMinHeight: '125px',
                        title: this.transPipe.transform('i18n::intelligent-search-flight.students-title'),
                        text: this.transPipe.transform('i18n::intelligent-search-flight.students-text'),
                        link: this.transPipe.transform('i18n::intelligent-search-flight.students-more-info-link'),
                        linkLabel: this.transPipe.transform('i18n::intelligent-search-flight.students-more-info-label'),
                        cancelLabel: this.transPipe.transform('i18n::intelligent-search-flight.students-cancel-label'),
                        confirmLabel: this.transPipe.transform('i18n::intelligent-search-flight.students-confirm-label'),
                        confirmCallback: () => {
                            this.lastSearchService.storeLastSearchFlight(this.formGroup.value);
                            const url = this.flightSearchService.for(this.formGroup.value, this.flightDto).build();
                            if (this.isLoadedInReactNative) {
                                this.postUrl(url);
                            } else {
                                this.window.open(url);
                            }
                            this.submitting = false;
                        },
                    },
                });
            } else {
                if (!noLastSearch) {
                    this.lastSearchService.storeLastSearchFlight(this.formGroup.value);
                }

                if (this.isRefxEnabled()) {
                    new BookingSearchJsonBuilder(this.formGroup.value, this._settings).buildRefxFormAndSubmit();
                } else {
                    const url = this.flightSearchService.for(this.formGroup.value, this.flightDto).build();

                    if (this.isLoadedInReactNative) {
                        this.postUrl(url);
                    } else {
                        window.open(url);
                    }
                }
                this.submitting = false;
            }
        }
    }

    private isRefxEnabled(): boolean {
        return this.cookiesService.hasCookie(this.refxCookies);
    }

    goToNextTab(el: HTMLElement) {
        TabIndexHelper.goToNextTabIndexedElement(this.window.document, el);
    }

    goToPreviousTab(el: HTMLElement) {
        TabIndexHelper.goToPreviousTabIndexedElement(this.window.document, el);
    }

    isDatepickerDisabled(segment: segmentKey = 'segment1') {
        return !this.formGroup.get(segment).get('arrival').value || this.isAnywhereDestinationSelected;
    }

    private isUMNR(): boolean {
        const passengers = this.formGroup.get('passengers');
        return (
            this.flightType !== FlightType.MultiCity &&
            passengers.value &&
            PassengerPickerListHelper.isExclusive(passengers.value, 'children')
        );
    }


    private isStudents(): boolean {
        const passengers = this.formGroup.get('passengers');
        return passengers?.value && PassengerPickerListHelper.isExclusive(passengers.value, 'students');
    }

    private isSameLocation = (prev: Location, curr: Location) => prev && curr && prev.code === curr.code;


    public onLastSearchCardClick(lastSearch: LastSearchDestination[]) {
        if (!lastSearch || !lastSearch.length) {
            console.warn('Last search data unavailable!');
            return;
        }

        // last search of multi_city is not supported
        const search = lastSearch[0];
        if (search.productType === ProductType.FLIGHT) {
            this.tabService.setActiveIndex(0);

            const mappedLastSearchData = this.mapLastSearchData(search);
            this.currentMappedLastSearchData = mappedLastSearchData;
            // need to wait for the tab to be set active
            setTimeout(() => {
                this.lastSearchService.updateLastSearchData(mappedLastSearchData);
                this.patchFormWithoutTriggeringValidation(mappedLastSearchData);
                this.runAllValidations();
                this.onSubmit(true);

            }, 500);
            // scroll into view of search mask
            const searchMask = this.window.document.querySelector('is-intelligent-search-tabs');

            if (!ScrollHelper.isInViewport(searchMask)) {
                ScrollHelper.scrollIntoViewWithOptions(searchMask, {
                    behavior: ScrollHelper.Behavior.Smooth,
                    block: ScrollHelper.Align.Start
                });
                (searchMask as HTMLElement).style.setProperty('scroll-margin-top', '20px');
            }
        }
    }


    private mapLastSearchData(search: LastSearchDestination): FlightFormMCT {
        return {
            flightType: search.tripType as FlightType,
            segment1: {
                arrival: {
                    code: search.destinationCity,
                    type: LocationType.LAST_SEARCH,
                    label: search.destinationCityLabel,
                },
                dateRange: {
                    departure: new Date(search.departureTs),
                    return: new Date(search.arrivalTs),
                },
                departure: {
                    code: search.originCity,
                    type: LocationType.LAST_SEARCH,
                    label: search.originCityLabel,
                },
            },
            passengers: {
                adults: search.adultsNbr,
                youths: search.youthsNbr,
                children: search.childrenNbr,
                infants: search.infantsNbr,
                students: 0
            },
            passengersBirthdates: {
                children: [],
            },
        };
    }


    private patchFormWithoutTriggeringValidation(data: FlightFormMCT) {
        this.formGroup.patchValue(data, { emitEvent: true });
    }

    // check the type, maybe later this method might be used also for FlightHotelForm
    private syncDatepickers(mappedLastSearchData: FlightFormMCT) {
        const dateRange = mappedLastSearchData?.segment1?.dateRange;
        const segment1Datepicker = this.datepickers.segment1;

        if (segment1Datepicker && dateRange) {
            segment1Datepicker.currentInternalDateRangeGroup.setValue(dateRange);
            segment1Datepicker.fakeParentFormGroup.get('dateRange').setValue(dateRange);
            segment1Datepicker.internalCalendarGroup.get('dateRange').updateValueAndValidity();

            this.datepickerConfiguration = {
                ...this.datepickerConfiguration,
                openOverlayByDefault: false,
            };
        }
    }

    private runAllValidations() {
        this.validateDestinationFields();
        this.validatePassengerFields();
        this.validateDateRange();
    }

    private validateDestinationFields() {
        const departureControl = this.formGroup.get('segment1').get('departure');
        const arrivalControl = this.formGroup.get('segment1').get('arrival');

        if (departureControl && arrivalControl) {
            const iataList = this.departuresLocation.map(loc => ({ code: loc.code }));

            departureControl.setValidators([
                Validators.required,
                requiredDistinctRoute(this.formGroup),
                ValidatorHelper.destinationAvailabilityValidator(iataList)
            ]);

            arrivalControl.setValidators([
                Validators.required,
                requiredDistinctRoute(this.formGroup),
                ValidatorHelper.destinationAvailabilityValidator(this.arrivalsLocation.segment1)
            ]);

            [departureControl, arrivalControl].forEach(ctrl => {
                ctrl.updateValueAndValidity();
                ctrl.markAsTouched();
                ctrl.markAsDirty();
            });
        }
    }

    private validatePassengerFields() {
        const birthdatesControl = this.formGroup.get('passengersBirthdates');
        birthdatesControl?.updateValueAndValidity({ onlySelf: false, emitEvent: true });
        birthdatesControl?.markAllAsTouched();
        birthdatesControl?.markAsDirty();
    }

    private validateDateRange() {
        const dateRangeGroup = this.formGroup.get('segment1').get('dateRange') as TypedControl<DateRange>;

        if (dateRangeGroup) {
            dateRangeGroup.clearValidators();
            dateRangeGroup.markAsUntouched();
            dateRangeGroup.markAsPristine();
            dateRangeGroup.setValidators([
                ValidatorHelper.dateRangeMatchAvailabilityValidator(() => this.datepickers.segment1.datesAvailability)
            ]);
            dateRangeGroup.updateValueAndValidity({ onlySelf: false, emitEvent: true });
            dateRangeGroup.markAllAsTouched();
            dateRangeGroup.markAsDirty();
        }
    }
}


