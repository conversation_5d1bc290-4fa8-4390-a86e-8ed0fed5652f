import { DateAvailability } from '../components';
import { AvailableRedirectAirport, AvailableRedirectDestination } from '../enums';


/**
 * TODO: need to be removed after the redirection period is over (USER-STORY 9940
 * Redirection const DWC
 */
const redirectPeriodDates = [
    { start: new Date('2025-04-07'), end: new Date('2025-04-11') },
    { start: new Date('2025-04-14'), end: new Date('2025-04-18') },
    { start: new Date('2025-04-21'), end: new Date('2025-04-25') },
    { start: new Date('2025-04-28'), end: new Date('2025-05-02') },
];

const possibleRedirectPeriodDates = [
    new Date('2025-04-09'),
    new Date('2025-04-16'),
    new Date('2025-04-23'),
    new Date('2025-04-30'),
    new Date('2025-04-10'),
    new Date('2025-04-17'),
    new Date('2025-04-24'),
    new Date('2025-05-01'),
];

const flexPackageDuration = 3;

export class DateHelper {

    public static isInAvailableDates = (date: Date, availableDates: Date[]): boolean => {
        return !!availableDates.find(d => {
            return d.toISOString().slice(0, 10) === date.toISOString().slice(0, 10);
        });
    }

    public static isInPromoDates = (
        date: Date,
        availableDates: DateAvailability[]): boolean => {
        return !!availableDates.find(d => {
            return d.date.getTime() === date.getTime() && d.isPromo;
        });
    }

    public static isLimitPromo = (
        date: Date,
        startDate: Date,
        endDate: Date,
        availableDates: DateAvailability[]): boolean => {
        if (!startDate) {
            startDate = new Date();
        }

        if (!endDate) {
            endDate = new Date();
            endDate.setFullYear((new Date()).getFullYear() + 1); // next year
        }

        return DateHelper.isBetweenDates(date, startDate, endDate) && DateHelper.isInPromoDates(date, availableDates);
    }


    public static isBetweenDates = (
        date: Date,
        startDate: Date,
        endDate: Date): boolean => {
        return DateHelper.isGreatherEqualThan(date, startDate) && DateHelper.isLessEqualThan(date, endDate);
    }

    public static isGreatherEqualThan = (date: Date, dateToCompare: Date): boolean => {
        return date.getTime() >= dateToCompare.getTime();
    }

    public static isLessEqualThan = (date: Date, dateToCompare: Date): boolean => {
        return date.getTime() <= dateToCompare.getTime();
    }

    public static isGreatherThan = (date: Date, dateToCompare: Date): boolean => {
        return date.getTime() > dateToCompare.getTime();
    }

    public static isSame = (date: Date, dateToCompare: Date): boolean => {
        return date.getTime() === dateToCompare.getTime();
    }

    public static isSameMonth = (date: Date, dateToCompare: Date): boolean => {
        return date.getMonth() === dateToCompare.getMonth();
    }

    public static yearsAgo(currentDate: Date, years: number, nextDay: boolean = false): Date {
        const yearsAgo = new Date(currentDate);
        yearsAgo.setFullYear(currentDate.getFullYear() - years);
        if (nextDay) {
            yearsAgo.setDate(yearsAgo.getDate() + 1);
        }
        return yearsAgo;
    }

    public static getRangeofDates = (date: Date, days: number, isBefore: boolean = false): Date[] => {
        const range = [];
        if (!date) {
            return range;
        }

        if (isBefore) {
            for (let i = 0; i < days; i++) {
                range.push(new Date(date.getTime() - (i * 24 * 60 * 60 * 1000)));
            }
        } else {
            for (let i = 0; i < days; i++) {
                range.push(new Date(date.getTime() + (i * 24 * 60 * 60 * 1000)));
            }
        }
        return range;
    }

    /**
     * TODO: need to be removed after the redirection period is over (USER-STORY 9940
     */
    public static isRedirectPeriod(startDate: Date, endDate: Date, iataCode: string, isDateFlexible: boolean): boolean {


        if (iataCode === AvailableRedirectAirport.DWC) {
            return DateHelper.isRedirectPeriodDwc(startDate, endDate);
        }

        if (iataCode === AvailableRedirectDestination.DWC || iataCode === AvailableRedirectDestination.UAE) {
            return DateHelper.isRedirectPeriodPackage(startDate, endDate, isDateFlexible);
        }

        return false;
    }


    public static isRedirectPeriodDwc(fromDate: Date, toDate: Date | undefined): boolean {
        if (!fromDate && !toDate) {
            return false;
        }
        const dates = [fromDate, toDate].filter(Boolean);
        // Check if dates are within the redirect period
        return this.areAnyDatesWithinAnyInterval(dates, redirectPeriodDates);

    }

    public static isRedirectPeriodPackage(fromDate: Date, toDate: Date | undefined, isDateFlexible: boolean): boolean {
        if (!fromDate && !toDate) {
            return false;
        }

        let startDate = fromDate;
        let endDate = toDate;
        // when check the startDate is part of redirection Day
        const isPossibleRedirectDaySelected = possibleRedirectPeriodDates.some(date =>
            date.getDate() === startDate.getDate()
            && date.getMonth() === startDate.getMonth()
            && date.getFullYear() === startDate.getFullYear());
        if (isDateFlexible) {
            startDate = new Date(startDate);
            startDate.setDate(startDate.getDate() - flexPackageDuration);

            if (endDate) {
                endDate = new Date(endDate);
                endDate.setDate(endDate.getDate() + flexPackageDuration);
            }
        }
        return endDate ?
            possibleRedirectPeriodDates.some(date => DateHelper.isBetweenDates(date, startDate, endDate)) : isPossibleRedirectDaySelected;

    }


    private static areAnyDatesWithinAnyInterval(
        dates: Date[],
        intervals: { start: Date; end: Date }[],
    ): boolean {
        return dates.some((date) =>
            intervals.some(
                (interval) => date >= interval.start && date <= interval.end,
            ),
        );
    }

}
