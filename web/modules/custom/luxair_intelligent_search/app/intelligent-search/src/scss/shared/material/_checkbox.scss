// CHECKBOX
$form-checkbox-text-color-active: $dark-gray;
$form-checkbox-text-color-disabled: $text-color-disabled;
$form-checkbox-text-color-checked-disabled: $text-color-disabled;

$form-checkbox-background-color-active: #026581;
$form-checkbox-background-color-disabled: transparent;
$form-checkbox-background-color-checked-disabled: $lavender-blue;

$form-checkbox-border-color-active: #026581;
$form-checkbox-border-color-disabled: $lavender-blue;
$form-checkbox-border-color-checked-disabled: $lavender-blue;

mat-checkbox {
    &.mat-checkbox {
        &.mat-checkbox-checked {
            .mat-checkbox-label {
                color: $form-checkbox-text-color-active;
            }

            .mat-checkbox-background {
                background-color: $form-checkbox-background-color-active;
            }

            .mat-checkbox-frame {
                border-color: $form-checkbox-border-color-active;
            }
        }

        &:hover:not(.mat-checkbox-disabled) {
            .mat-checkbox-frame {
                border-color: $form-checkbox-border-color-active;
            }
        }

        &.mat-checkbox-disabled {
            &.mat-checkbox-checked {
                .mat-checkbox-background {
                    background-color: $form-checkbox-background-color-checked-disabled;
                }
            }

            .mat-checkbox-label {
                color: $form-checkbox-text-color-disabled;
            }

            .mat-checkbox-background {
                background-color: $form-checkbox-background-color-disabled;
            }

            .mat-checkbox-frame {
                border-color: $form-checkbox-border-color-disabled;
            }
        }

        .mat-checkbox-layout {
            display: flex;
        }
    }
}
