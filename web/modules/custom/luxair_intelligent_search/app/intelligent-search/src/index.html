<!DOCTYPE html>
<html lang="en" data-luxair-environment="acceptance">

<head>
    <meta charset="utf-8" />
    <title>IntelligentSearch</title>
    <base href="/" />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />

    <!-- Added styles and fronts copied from Luxair.lu -->
    <link rel="stylesheet" media="all"
        href="https://www.luxair.lu/sites/default/files/css/css_hKwYtJWWgL5VKWWs_r_x7heqnLE-u6pe8r0yScIw0Og.css" />
    <link rel="stylesheet" media="all"
        href="https://www.luxair.lu/sites/default/files/css/css_xOschrmVo72SQJ7gs2RIaOsUic2UZHPcXfGU0dfJL3k.css" />
    <link rel="preload" data-info="exo2 latin 100-900"
        href="https://fonts.gstatic.com/s/exo2/v9/7cHmv4okm5zmbtYoK-4.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="stylesheet" media="all"
        href="https://www.luxair.lu/sites/default/files/css/css_nYRT7s8JyNSTXtfhI98ekrN68okbZ1BZK_g3opRUAwA.css" />

</head>

<body>
    <div class="luxair">
        <luxair-shell currentwebsite="LGIT"></luxair-shell>
        <is-intelligent-search-tabs active_tab_index="1">
            <is-tab title="Flight" icon="airplane-tilted">
                <div>
                    <div class="visually-hidden">
                        Intelligent Flight Search
                    </div>
                    <div>
                        <is-intelligent-flight-search business="" jump_on_first_promo_date=""
                            routes_form_id="FLIGHTS_LUXAIR_LU" default_departure="LUX" default_arrival="" promo="0"
                            calendar_filter_type="" calendar_filter_value="" cid="" disable_calendar="0"
                            disable_histogram="0" intelligent_default_tab="0" datepicker_initially_open="0"
                            default_trip_duration="7" adults_increment="1" disable_adults="0" disable_students="1"
                            disable_youths="0" disable_children="0" disable_infants="0" companion_fare="0"
                            students_count="" youths_count="" show_student_message="1" anywhere="0" last_search="1"
                            flight_types="round_trip,one_way,multi_city"
                            default_flight_type="round_trip"></is-intelligent-flight-search>
                    </div>
                </div>
            </is-tab>
            <is-tab title="LuxairTours Package" icon="palm">
                <div>
                    <div class="visually-hidden">
                        Application Intelligent Package Search
                    </div>
                    <div>
                        <is-intelligent-flight-hotel-search datepicker_mode="flexible" date_flexible="true"
                            default_destination="" filter_dest="" hotel_code="" category="" adults_count="2"
                            children_count="" date_from="" date_to="" maximum_price="" boards="" themes="" catalogue=""
                            pricetypes="" rooms="" history_value="" travel_lifetype_type="" travel_lifetime=""
                            travel_number="" anywhere="0" last_search="1"></is-intelligent-flight-hotel-search>
                    </div>
                </div>
            </is-tab>
            <is-tab title="City Break: Flight + Hotel " icon="city">
                <div>
                    <div class="visually-hidden">
                        Application Intelligent City Break Search
                    </div>
                    <div>
                        <is-intelligent-city-break-search default_departure="LUX" default_arrival="" adults_count="2"
                            children_count="0" date_from="" date_to=""
                            routes_form_id="FLIGHTS_LUXAIR_LU"></is-intelligent-city-break-search>
                    </div>
                </div>
            </is-tab>
            <is-tab title="Car rental" icon="car">
                <div>
                    <div class="visually-hidden">
                        Application Intelligent Car Rental Search
                    </div>
                    <div>
                        <is-intelligent-car-rental-search default_pickup=""
                            default_return=""></is-intelligent-car-rental-search>
                    </div>
                </div>
            </is-tab>
        </is-intelligent-search-tabs>
    </div>
    <div id="feature-quick-action-card"></div>
    <div class="contaiener" style="height: 2000px;">
    </div>

    <script type="application/json" data-drupal-selector="drupal-settings-json">
            {
                "path": {
                    "baseUrl": "\/",
                    "scriptPath": null,
                    "pathPrefix": "en\/",
                    "currentPath": "node\/7",
                    "currentPathIsAdmin": false,
                    "isFront": true,
                    "currentLanguage": "en"
                },
                "pluralDelimiter": "\u0003",
                "suppressDeprecationErrors": true,
                "apiKey": {
                    "instant_search": "4725fa75-81de-4fee-b940-d5ebee581f48"
                },
                "luxair_intelligent_search": {
                    "B2BCheck": true,
                    "modulePath": "modules\/custom\/luxair_intelligent_search"
                },
                "luxair_api_endpoint": {
                    "AMADEUS_CHECKIN_URL": "https:\/\/checkin.luxair.lu",
                    "AMADEUS_DX_BOOKING_URL": "https:\/\/bookingconnector.luxair.lu\/booking",
                    "AMADEUS_DX_CORPORATE_BOOKING_URL": "https:\/\/bookingconnector.luxair.lu\/booking\/corporate",
                    "AMADEUS_DX_MY_BOOKING_URL": "https:\/\/bookingconnector.luxair.lu\/booking\/retrieve",
                    "AMADEUS_ERETAIL_BOOKING_URL": "https:\/\/www.luxair.lu\/pws\/booking\/ERetailService.action",
                    "AMADEUS_ERETAIL_CHECKIN_URL": "https:\/\/www.luxair.lu\/pws\/checkin\/CheckinService.action",
                    "AMADEUS_ERETAIL_MY_BOOKING_URL": "https:\/\/www.luxair.lu\/pws\/retrieve\/ATCService.action",
                    "AMADEUS_MERCI_BOOKING_URL": "https:\/\/www.luxair.lu\/pws\/booking\/ERetailServiceMobile.action",
                    "AMADEUS_MERCI_CHECKIN_URL": "https:\/\/www.luxair.lu\/pws\/checkin\/CheckinMobileService.action",
                    "BEWOTEC_IBE_URL": "https:\/\/booking.luxairtours.lu",
                    "CARTRAWLER_IBE_URL": "https:\/\/cars.cartrawler.com\/luxair",
                    "CITY_BREAK_IBE_URL": "https:\/\/citybreak.luxair.lu",
                    "KRONOS_SEAT_RESERVATION_URL": "https:\/\/asr.luxair.lu\/webbookingmodule\/login.jsf",
                    "LUXAIR_AGENCIES_API_URL": "https:\/\/api.luxair.lu\/luxairtours\/agencies",
                    "LUXAIR_AIRPORTS_API_URL": "https:\/\/api.luxair.lu\/luxair\/airports",
                    "LUXAIR_ALERTS_API_URL": "https:\/\/api.luxair.lu\/luxair\/alerts",
                    "LUXAIR_ARRIVAL_GUIDE_API_DESTINATIONS_OVERVIEW": "https:\/\/www.luxair.lu\/luxair_destinations_overview",
                    "LUXAIR_ARRIVAL_GUIDE_API_URL": "https:\/\/api.arrivalguides.com\/api",
                    "LUXAIR_CALENDAR_API_URL": "https:\/\/api.luxair.lu\/luxair\/calendar",
                    "LUXAIR_CAMPAIGNS_CONFIG_API_URL": "https:\/\/api.luxair.lu\/luxair\/campaigns-config",
                    "LUXAIR_CAMPAIGNS_REGISTRATION_API_URL": "https:\/\/api.luxair.lu\/myluxair\/profile\/digital-carnet\/register",
                    "LUXAIR_CITY_BREAK_DESTINATIONS_API_URL": "https:\/\/api.luxair.lu\/luxair\/destinations",
                    "LUXAIR_CONTACT_URL": "https:\/\/www.luxair.lu\/mobile\/contact\/",
                    "LUXAIR_CRM_FEEDBACK_API_URL": "https:\/\/www.luxair.lu\/proxy\/",
                    "LUXAIR_DESTINATION_PRICES": "https:\/\/api.luxair.lu\/luxairtours\/best-price",
                    "LUXAIR_DESTINATIONS_API_URL": "https:\/\/api.luxair.lu\/travel-ibe\/destinations",
                    "LUXAIR_ERETAIL_BOOKING_URL": "https:\/\/www.luxair.lu\/cms\/luxair.php",
                    "LUXAIR_FLIGHTS_API_URL": "https:\/\/api.luxair.lu\/luxair\/flights",
                    "LUXAIR_FORMS_API_URL": "https:\/\/api.luxair.lu\/luxair\/forms",
                    "LUXAIR_INSTANT_SEARCH": "https:\/\/api.luxair.lu\/instantsearch",
                    "LUXAIR_LOUNGES_API_URL": "https:\/\/api.luxair.lu\/luxair\/lounges",
                    "LUXAIR_NEWSLETTER_SUBSCRIBE_API_URL": "https:\/\/api.luxair.lu\/luxair\/newsletter",
                    "LUXAIR_PARTNERS_CONTACT_URL": "https:\/\/partners.luxair.lu\/partners.php?p=FR,5836,90,,1,,",
                    "LUXAIR_PROFILE_API_URL": "https:\/\/api.luxair.lu\/myluxair",
                    "LUXAIR_STATIC_SERVER_URL": "https:\/\/static.luxair.lu",
                    "LUXAIR_TEASER_API_URL": "https:\/\/api.luxair.lu\/luxairtours\/teaser\/Package",
                    "LUXAIR_WEB_CONTACT_FORM": "https:\/\/api.luxair.lu\/contact",
                    "LUXAIRTOURS_CALENDAR_API_URL": "https:\/\/api.luxair.lu\/travel-ibe\/availabilities",
                    "LUXAIRTOURS_CALENDAR_RETURN_API_URL": "https:\/\/api.luxair.lu\/travel-ibe\/calendar-return",
                    "TRAVEL_IBE_PRODUCTS_API_URL": "https:\/\/api.luxair.lu\/travel-ibe\/products",
                    "LUXAIR_REFX_URL": "https:\/\/uat.digital.airline.amadeus.com\/lg\/booking"
                },
                "flight_history_params": {
                    "flight_lifetype_type": null,
                    "flight_lifetime": null,
                    "flight_number": null,
                    "travel_lifetype_type": null,
                    "travel_lifetime": null,
                    "travel_number": null,
                    "car_lifetype_type": null,
                    "car_lifetime": null,
                    "car_number": null
                }
            }
        </script>

    <script type="text/javascript">
        window.drupalTranslations = {
            strings: {
                "": {
                    Apply: "Apply",
                    Home: "Home",
                    "instant-search.from_placeholder":
                        "From where to start?",
                    "instant-search.to_placeholder": "Where to?",
                    "instant-search.search-button": "Search",
                    "instant-search.duration_label": "Stay Duration",
                    "instant-search.passenger_label": "Passengers",
                    "instant-search.price_information":
                        "Price for return flight per 1 adult passenger and includes taxes and fees",
                    "instant-search.histogram_legend_lowest":
                        "Lowest price",
                    "instant-search.histogram_legend_selected": "Selected",
                    "instant-search.price_from": "from",
                    "instant-search.book_flight": "Book now",
                    "instant-search.no_flight_text":
                        "currently, no flights available",
                    "instant-search.departing_text": "Outbound flight:",
                    "instant-search.returning_text": "Inbound flight:",
                    "instant-search.no_histogram_data_text":
                        "We apologize! We were not able to find results for your search. Please try to adapt the trip duration (i.e. to 7 days) or other search criteria",
                    "instant-search.calendar_no_data":
                        "We apologize! We were not able to find results for your search. Please try to adapt the trip duration (i.e. to 7 days) or other search criteria",
                    "instant-search.no_departure_selected":
                        "select airport",
                    "instant-search.no_airport_result":
                        "select destination",
                    "instant-search.required": "Please select",
                    "instant-search.minimum_passenger_error":
                        "No passengers selected",
                    "instant-search.required_departure_airport":
                        "No departure selected",
                    "instant-search.required_arrival_airport":
                        "No destination selected",
                    "instant-search.passengers_count_error":
                        "Invalid number of passengers",
                    "instant-search.infants_count_error":
                        "Invalid number of infants",
                    "i18n::intelligent-search-flight.child-birthdate-error":
                        "Enter a valid child birthdate, must be of age 5 to 11 at the day of departure.",
                    "instant-search.summary_minimum_passenger_error":
                        "Please select at least 1 adult passenger",
                    "instant-search.summary_required_departure_airport":
                        "Please select the airport of origin",
                    "instant-search.summary_required_arrival_airport":
                        "Please select the airport of destination",
                    "instant-search.summary_passengers_count_error":
                        "Number of adults and children cannot exceed 9",
                    "instant-search.summary_infants_count_error":
                        "Number of infants cannot exceed the number of adults",
                    "instant-search.timeout":
                        "We apologize, the service is not accessible now. Please try again in several minutes.",
                    "instant-search.cache_based_price_disclaimer":
                        "All shown fares are Economy class return fares per 1 adult passenger and include taxes and fee. Due to regular data update, the shown prices may deviate from those available at the time of the booking request based on the available seats and fares.",
                    "instant-search.passenger": "@count passenger",
                    "instant-search.passengers": "@count passengers",
                    "instant-search.adult": "@count adult",
                    "instant-search.adults": "@count adults",
                    "instant-search.child": "@count child",
                    "instant-search.children": "@count children",
                    "instant-search.infant": "@count infant",
                    "instant-search.infants": "@count infants",
                    "instant-search.duration_day": "@count day",
                    "instant-search.duration_days": "@count days",
                    "instant-search.error_summary": "@count warning",
                    "instant-search.errors_summary": "@count warnings",
                    "instant-search.min": "Invalid stay duration",
                    "instant-search.summary_required":
                        "Please select the stay duration. It should be at least 1 day and at most 60 days.",
                    "instant-search.summary_min":
                        "Please select the stay duration. It should be at least 1 day and at most 60 days.",
                    "sme-contact-form.company": "Your company details",
                    "sme-contact-form.company_name": "Company name",
                    "sme-contact-form.company_address": "Company address",
                    "sme-contact-form.company_zip_code": "Zip code",
                    "sme-contact-form.company_city": "City",
                    "sme-contact-form.company_country": "Country",
                    "sme-contact-form.company_phone_code":
                        "Country phone code",
                    "sme-contact-form.company_phone_number": "Phone number",
                    "sme-contact-form.contact": "Your contact details",
                    "sme-contact-form.contact_title": "Title",
                    "sme-contact-form.contact_first_name": "First name",
                    "sme-contact-form.contact_last_name": "Last name",
                    "sme-contact-form.contact_job_title": "Job title",
                    "sme-contact-form.contact_email": "Email address",
                    "sme-contact-form.contact_phone_code":
                        "Country phone code",
                    "sme-contact-form.contact_phone_number": "Phone number",
                    "sme-contact-form.travel": "Your travel details",
                    "sme-contact-form.travel_flight_number":
                        "Number of roundtrip flights per year",
                    "sme-contact-form.travel_budget":
                        "Annual travel budget (in \u20ac)",
                    "sme-contact-form.travel_lg_destinations":
                        "Main Luxair destination(s) ",
                    "sme-contact-form.travel_class":
                        "What is your preferred cabin class?",
                    "sme-contact-form.travel_message": "Message",
                    "sme-contact-form.form_submit": "Submit",
                    "sme-contact-form.notification_error_message":
                        "Sorry, something went wrong! Please try again.",
                    "sme-contact-form.notification_success_message":
                        "Thank you for your message! We will contact you as soon as possible.",
                    "sme-contact-form.notification_dismiss": "Ok",
                    "sme-contact-form.error_required": "Info is required",
                    "sme-contact-form.error_email":
                        "Please check the email format",
                    "sme-contact-form.error_number":
                        "Please check the format of the phone number",
                    "sme-contact-form.error_atLeastOne":
                        "Please select at least one value",
                    "sme-contact-form.error_isInList":
                        "Please select from the list",
                    "sme-contact-form.title_mr": "Mr.",
                    "sme-contact-form.title_mrs": "Mrs.",
                    "sme-contact-form.travel_class_eco": "Economy class",
                    "sme-contact-form.travel_class_business":
                        "Business class",
                    "instant-search.max": "Invalid stay duration",
                    "instant-search.summary_max":
                        "Please select a shorter stay duration. The duration is between 1 and 60 days.",
                    "myluxair.register-feedback-message":
                        "Thank you for registering! You will be sent a confirmation once the service has been activated.",
                    "myluxair.newsletter-feedback-message":
                        "Thank you for registering!",
                    "web-contact-form.contact-us": "Contact us",
                    "web-contact-form.who-are-you": "Who are you?",
                    "web-contact-form.i-am-a-traveller":
                        "I am a private customer",
                    "web-contact-form.i-am-a-travel-agency":
                        "I am a travel agency (flight only)",
                    "web-contact-form.i-am-a-leisure-agency":
                        "I am a travel agency (flight + hotel)",
                    "web-contact-form.error-required": "Required",
                    "web-contact-form.error-is-in-list":
                        "Please select from the list",
                    "web-contact-form.error-is-same-value":
                        "Please enter the same email address",
                    "web-contact-form.error-number": "Invalid format",
                    "web-contact-form.error-email": "Invalid format",
                    "web-contact-form.traveller-next": "Next",
                    "web-contact-form.traveller-hotline-phone-number": " ",
                    "web-contact-form.traveller-step-who-are-you":
                        "Who are you?",
                    "web-contact-form.traveller-i-am-a-traveller":
                        "A private customer",
                    "web-contact-form.traveller-step-select-a-topic":
                        "Categories",
                    "web-contact-form.traveller-category-selector-go-to-parent":
                        "Back",
                    "web-contact-form.traveller-category-selector-dialog-reset-title":
                        "Do you want to change the category?",
                    "web-contact-form.traveller-category-selector-dialog-reset-message":
                        "By confirming this action, you will lose some data that has already been entered.",
                    "web-contact-form.traveller-category-selector-dialog-reset-cancel":
                        "Cancel",
                    "web-contact-form.traveller-category-selector-dialog-reset-approve":
                        "Confirm",
                    "web-contact-form.traveller-reset-select-a-topic":
                        "You have chosen the following category:",
                    "web-contact-form.traveller-reset-select-a-topic-change-button":
                        "Change category",
                    "web-contact-form.traveller-step-booking-reference":
                        "Reference",
                    "web-contact-form.traveller-booking-reference":
                        "Booking reference or ticket number",
                    "web-contact-form.traveller-passenger-name":
                        "Last name",
                    "web-contact-form.traveller-i-have-no-reference":
                        "I don\u2019t have a booking reference",
                    "web-contact-form.traveller-please-provide-valid-booking-reference":
                        "Please enter a valid booking reference",
                    "web-contact-form.traveller-step-message": "Message",
                    "web-contact-form.traveller-comment":
                        "Please enter your message",
                    "web-contact-form.traveller-prefered-language":
                        "Preferred language of communication",
                    "web-contact-form.traveller-prefered-language-english":
                        "English",
                    "web-contact-form.traveller-prefered-language-french":
                        "Fran\u00e7ais",
                    "web-contact-form.traveller-prefered-language-german":
                        "Deutsch",
                    "web-contact-form.traveller-prefered-language-luxembourgish":
                        "L\u00ebtzebuergesch",
                    "web-contact-form.traveller-message-add-document-or-picture":
                        "Add a document",
                    "web-contact-form.traveller-message-accepted-files":
                        "Accepted formats: JPG, PNG, PDF",
                    "web-contact-form.traveller-step-contact-informations":
                        "Contact details",
                    "web-contact-form.traveller-contact-firstname":
                        "First name",
                    "web-contact-form.traveller-contact-lastname":
                        "Last name",
                    "web-contact-form.traveller-contact-email":
                        "E-mail address",
                    "web-contact-form.traveller-contact-confirm-email":
                        "Confirm e-mail address",
                    "web-contact-form.traveller-contact-phone-code":
                        "Prefix",
                    "web-contact-form.traveller-contact-phone":
                        "Telephone number",
                    "web-contact-form.traveller-contact-title": "Title",
                    "web-contact-form.traveller-title-mr": "Mr",
                    "web-contact-form.traveller-title-mrs": "Mrs",
                    "web-contact-form.traveller-step-summary": "Summary",
                    "web-contact-form.traveller-summary-please-verify-your-request":
                        "Please check your details",
                    "web-contact-form.traveller-summary-something-goes-wrong":
                        "An error has occurred. Please try again later.",
                    "web-contact-form.traveller-summary-gdrp-consent":
                        "Your personal data will be processed in line with our ",
                    "web-contact-form.traveller-summary-gdrp-consent-link":
                        "https:\/\/www.luxair.lu\/en\/information\/security-and-privacy-policy",
                    "web-contact-form.traveller-summary-gdrp-consent-link-label":
                        "security and privacy policy.",
                    "web-contact-form.traveller-number-of-files-attached":
                        "Document(s) attached",
                    "web-contact-form.traveller-step-thank-you":
                        "Confirmation",
                    "web-contact-form.traveller-thank-you-your-request-has-been-correctly-sent":
                        "Your request has been sent. Our teams will contact you as soon as possible.",
                    "web-contact-form.traveller-thank-you-for-urgent-assistance-please-call":
                        " ",
                    "web-contact-form.traveller-thank-you-go-to-home-page":
                        "Return to home page",
                    "web-contact-form.traveller-thank-you": "Thank you",
                    "web-contact-form.traveller-thank-you-schedule-of-hotline":
                        " ",
                    "web-contact-form.travel-agency-next": "Next",
                    "web-contact-form.travel-agency-hotline-phone-number":
                        " ",
                    "web-contact-form.travel-agency-step-who-are-you":
                        "Who are you?",
                    "web-contact-form.travel-agency-i-am-a-travel-agency":
                        "A travel agency (flight only)",
                    "web-contact-form.travel-agency-step-login":
                        "Identification",
                    "web-contact-form.travel-agency-login-as-iata":
                        "IATA agency",
                    "web-contact-form.travel-agency-login-as-non-iata":
                        "Non-IATA agency",
                    "web-contact-form.travel-agency-login-agency-iata":
                        "IATA number",
                    "web-contact-form.travel-agency-login-agency-name":
                        "Agency name",
                    "web-contact-form.travel-agency-step-select-a-topic":
                        "Categories",
                    "web-contact-form.travel-agency-category-selector-go-to-parent":
                        "Back",
                    "web-contact-form.travel-agency-category-selector-dialog-reset-title":
                        "Do you want to change the category?",
                    "web-contact-form.travel-agency-category-selector-dialog-reset-message":
                        "By confirming this action, you will lose some data that has already been entered.",
                    "web-contact-form.travel-agency-category-selector-dialog-reset-cancel":
                        "Cancel",
                    "web-contact-form.travel-agency-category-selector-dialog-reset-approve":
                        "Confirm",
                    "web-contact-form.travel-agency-reset-select-a-topic":
                        "You have chosen the following category:",
                    "web-contact-form.travel-agency-reset-select-a-topic-change-button":
                        "Change category",
                    "web-contact-form.travel-agency-step-booking-reference":
                        "Reference",
                    "web-contact-form.travel-agency-booking-reference":
                        "Booking reference or ticket number",
                    "web-contact-form.travel-agency-passenger-name":
                        "Passenger last name",
                    "web-contact-form.travel-agency-i-have-no-reference":
                        "I don\u2019t have a booking reference",
                    "web-contact-form.travel-agency-please-provide-valid-booking-reference":
                        "Please enter a valid booking reference",
                    "web-contact-form.travel-agency-step-message":
                        "Message",
                    "web-contact-form.travel-agency-comment":
                        "Please enter your message",
                    "web-contact-form.travel-agency-prefered-language":
                        "Preferred language of communication",
                    "web-contact-form.travel-agency-prefered-language-english":
                        "English",
                    "web-contact-form.travel-agency-prefered-language-french":
                        "Fran\u00e7ais",
                    "web-contact-form.travel-agency-prefered-language-german":
                        "Deutsch",
                    "web-contact-form.travel-agency-prefered-language-luxembourgish":
                        "L\u00ebtzebuergesch",
                    "web-contact-form.travel-agency-message-add-document-or-picture":
                        "Add a document",
                    "web-contact-form.travel-agency-message-accepted-files":
                        "Accepted formats: JPG, PNG, PDF",
                    "web-contact-form.travel-agency-step-passenger-informations":
                        "Passenger details",
                    "web-contact-form.travel-agency-passenger-title":
                        "Title",
                    "web-contact-form.travel-agency-passenger-firstname":
                        "Passenger\u2019s first name",
                    "web-contact-form.travel-agency-passenger-lastname":
                        "Passenger\u2019s last name",
                    "web-contact-form.travel-agency-passenger-corporate-title":
                        "Business traveller?",
                    "web-contact-form.travel-agency-passenger-corporate":
                        "Company name",
                    "web-contact-form.travel-agency-step-contact-informations":
                        "Agency details",
                    "web-contact-form.travel-agency-contact-firstname":
                        "First name",
                    "web-contact-form.travel-agency-contact-lastname":
                        "Last name",
                    "web-contact-form.travel-agency-contact-email":
                        "E-mail address",
                    "web-contact-form.travel-agency-contact-confirm-email":
                        "Confirm e-mail address",
                    "web-contact-form.travel-agency-contact-phone-code":
                        "Prefix",
                    "web-contact-form.travel-agency-contact-phone":
                        "Telephone number",
                    "web-contact-form.travel-agency-contact-title": "Title",
                    "web-contact-form.travel-agency-title-mr": "Mr",
                    "web-contact-form.travel-agency-title-mrs": "Mrs",
                    "web-contact-form.travel-agency-contact-agency-country":
                        "Country",
                    "web-contact-form.travel-agency-contact-agency-city":
                        "City",
                    "web-contact-form.travel-agency-step-summary":
                        "Summary",
                    "web-contact-form.travel-agency-summary-please-verify-your-request":
                        "Please check your details",
                    "web-contact-form.travel-agency-summary-something-goes-wrong":
                        "An error has occurred. Please try again later.",
                    "web-contact-form.travel-agency-summary-gdrp-consent":
                        "Your personal data will be processed in line with our ",
                    "web-contact-form.travel-agency-summary-gdrp-consent-link":
                        "https:\/\/www.luxair.lu\/en\/information\/security-and-privacy-policy",
                    "web-contact-form.travel-agency-summary-gdrp-consent-link-label":
                        "security and privacy policy.",
                    "web-contact-form.travel-agency-number-of-files-attached":
                        "Document(s) attached",
                    "web-contact-form.travel-agency-step-thank-you":
                        "Confirmation",
                    "web-contact-form.travel-agency-thank-you-your-request-has-been-correctly-sent":
                        "Your request has been sent. Our teams will contact you as soon as possible.",
                    "web-contact-form.travel-agency-thank-you-for-urgent-assistance-please-call":
                        " ",
                    "web-contact-form.travel-agency-thank-you-go-to-home-page":
                        "Return to home page",
                    "web-contact-form.travel-agency-thank-you": "Thank you",
                    "web-contact-form.travel-agency-thank-you-schedule-of-hotline":
                        " ",
                    "web-contact-form.error-is-an-allowed-format":
                        "Invalid format",
                    "web-contact-form.error-is-size-limit-exceeded":
                        "Maximum file size limit has been exceeded.",
                    "web-contact-form.traveller-go-back-dialog-reset-title":
                        "You want to start over?",
                    "web-contact-form.traveller-go-back-dialog-reset-message":
                        "By confirming this action, you will lose some data that has already been entered.",
                    "web-contact-form.traveller-go-back-dialog-reset-cancel":
                        "Cancel",
                    "web-contact-form.traveller-go-back-dialog-reset-approve":
                        "Confirm",
                    "web-contact-form.traveller-step-refund":
                        "Compensation",
                    "web-contact-form.traveller-account-holder":
                        "Account holder",
                    "web-contact-form.traveller-iban": "IBAN number",
                    "web-contact-form.traveller-bic": "SWIFT\/BIC code",
                    "web-contact-form.traveller-i-have-no-iban":
                        "I do not want to provide my bank account details",
                    "web-contact-form.travel-agency-go-back-dialog-reset-title":
                        "You want to start over?",
                    "web-contact-form.travel-agency-go-back-dialog-reset-message":
                        "By confirming this action, you will lose some data that has already been entered.",
                    "web-contact-form.travel-agency-go-back-dialog-reset-cancel":
                        "Cancel",
                    "web-contact-form.travel-agency-go-back-dialog-reset-approve":
                        "Confirm",
                    "web-contact-form.error-iban-not-valid":
                        "Invalid IBAN number",
                    "web-contact-form.traveller-booking-reference-informations":
                        "The booking reference can be found on your confirmation and looks like this: \u003Cb\u003E3OTCHJ\u003C\/b\u003E or \u003Cb\u003E6831549\u003C\/b\u003E\r\n\u003Cbr\u003E\u003Cbr\u003E\r\nThe ticket number can also be found on your booking confirmation and looks like this: \u003Cb\u003E1492400176327\u003C\/b\u003E",
                    "web-contact-form.traveller-iban-informations":
                        "If you are eligible for a refund, we will be able to process your request more quickly if you can provide the following details in advance.",
                    "web-contact-form.travel-agency-booking-reference-informations":
                        "The booking reference can be found on your confirmation and looks like this: \u003Cb\u003E3OTCHJ\u003C\/b\u003E\r\n\u003Cbr\u003E\u003Cbr\u003E\r\nThe ticket number can also be found on your booking confirmation and looks like this: \u003Cb\u003E1492400176327\u003C\/b\u003E",
                    "web-contact-form.traveller-input-operator":
                        "Which product does your inquiry refer to?",
                    "web-contact-form.traveller-input-operator-lg":
                        "Luxair flight",
                    "web-contact-form.traveller-input-operator-lgit":
                        "LuxairTours package (flight + hotel)",
                    "web-contact-form.traveller-step-pnr-flight-segment-selection":
                        "Flight",
                    "web-contact-form.traveller-flight-segment-selection":
                        "Please select the concerned flight",
                    "web-contact-form.travel-agency-step-pnr-flight-segment-selection":
                        "Flight",
                    "web-contact-form.travel-agency-flight-segment-selection":
                        "Please select the concerned flight",
                    "instant-search.youths_fare_error": "warning",
                    "instant-search.summary_youths_fare_error":
                        "The youth fare cannot be booked online together with a child and\/or an infant",
                    "instant-search.adults_range_age": "  ",
                    "instant-search.youth": "@count youth",
                    "instant-search.youths": "@count youths",
                    "instant-search.youths_range_age": "12-24 years",
                    "instant-search.children_range_age": "2-11 years",
                    "instant-search.infants_range_age": "0-1 years",
                    "web-contact-form.traveller-back": "Back",
                    "web-contact-form.traveller-complete-last-step":
                        "Submit",
                    "web-contact-form.travel-agency-back": "Back",
                    "web-contact-form.travel-agency-complete-last-step":
                        "Submit",
                    "i18n::intelligent-search-common.count-passengers":
                        "@count Passengers",
                    "i18n::intelligent-search-common.clear": "Reset",
                    "i18n::intelligent-search-common.apply": "Apply",
                    "i18n::intelligent-search-common.minimum-passenger-error":
                        "Please add at least one passenger",
                    "i18n::intelligent-search-common.maximum-passenger-error":
                        "The total number of passengers cannot exceed 9",
                    "i18n::intelligent-search-common.youth-fare-error":
                        "The youth fare cannot be booked online together with a child and\/or an infant",
                    "i18n::intelligent-search-common.infants-count-error":
                        "The number of infants cannot exceed the number of adults",

                    "i18n::intelligent-search-flight.passengers":
                        "Passengers",
                    "i18n::intelligent-search-flight.adults": "Adults",
                    "i18n::intelligent-search-flight.18-years-and-more":
                        "Over 18 years",
                    "i18n::intelligent-search-flight.youths": "Youth",
                    "i18n::intelligent-search-flight.12-24-years":
                        "12-24 years",
                    "i18n::intelligent-search-flight.children": "Children",
                    "i18n::intelligent-search-flight.2-11-years":
                        "2-11 years",
                    "i18n::intelligent-search-flight.infants": "Infants",
                    "i18n::intelligent-search-flight.0-1-year": "0-1 year",
                    "i18n::intelligent-search-common.loading": "Loading...",
                    "i18n::intelligent-search-common.departure-date":
                        "Departure date",
                    "i18n::intelligent-search-common.departure":
                        "Departure",
                    "i18n::intelligent-search-common.return-date":
                        "Return date",
                    "i18n::intelligent-search-common.return": "Return",
                    "i18n::intelligent-search-common.date-error":
                        "Invalid date selection, please adapt!",
                    "i18n::intelligent-search-common.date-error-departure":
                        "The selected departure date is not valid any more",
                    "i18n::intelligent-search-common.date-error-return":
                        "The selected return date is not valid any more!",
                    'i18n::intelligent-search-common.date-error-departure-and-return':
                        "Dates could not be applied, select a valid travel period.",
                    "i18n::intelligent-search-common.date-required-return":
                        "It is required to select a return date",
                    "i18n::intelligent-search-flight.round-trip":
                        "Round-trip",
                    "i18n::intelligent-search-flight.one-way": "One-way",
                    "i18n::intelligent-search-flight.promo-legend":
                        "Lowest fare",
                    "i18n::intelligent-search-flight.tab-title-calendar":
                        "Calendar",
                    "i18n::intelligent-search-flight.tab-title-histogram":
                        "Best Prices",
                    "i18n::intelligent-search-flight.trip-duration":
                        "Trip duration",
                    "i18n::intelligent-search-flight.(in-days)":
                        "(in days)",
                    "i18n::intelligent-search-flight.histogram-from":
                        "From",
                    "i18n::intelligent-search-flight.flight-no-availability":
                        "No flights available",
                    "i18n::intelligent-search-flight.histogram-no-availability":
                        "No availability found for this route",
                    "i18n::intelligent-search-flight.tab-histogram-not-available":
                        "Unfortunately there are no flights available for this trip duration. Please try to adjust the duration (e.g. 7 days) or other search criteria.",
                    "i18n::intelligent-search-flight.best-price-for-a-round-trip":
                        "Best price for a round trip",
                    "i18n::intelligent-search-flight.price-for-return-flight-per-1-adult-passenger-and-includes-taxes-and-fees":
                        "Price for return flight per 1 adult passenger and includes taxes and fees.",
                    "i18n::intelligent-search-flight.origin": "From",
                    "i18n::intelligent-search-flight.where-are-you-going-from":
                        "Where from?",
                    "i18n::intelligent-search-flight.departure-required":
                        "Please select a departure airport.",
                    "i18n::intelligent-search-flight.no-result":
                        "No results have been found.",
                    "i18n::intelligent-search-flight.destination": "To",
                    "i18n::intelligent-search-flight.where-are-you-going":
                        "Where to?",
                    "i18n::intelligent-search-flight.arrival-required":
                        "The arrival is required.",
                    "i18n::intelligent-search-flight.destination-invalid":
                        "Destination no longer available, select another.",
                    "i18n::intelligent-search-flight.search": "Search",
                    "i18n::intelligent-search-flight-hotel.departure-and-return-3days":
                        "+\/- 3 days",
                    "i18n::intelligent-search-common.children-ages-related-to-adult-error":
                        "The number of children under 2 years of age cannot exceed the number of adults.",
                    "i18n::intelligent-search-common.children-age-range-error":
                        "Child age must be between 0 and 17 years.",
                    "i18n::intelligent-search-flight-hotel.age-of-the-child":
                        "Age of the child",
                    "i18n::intelligent-search-flight-hotel.on-the-day-of-departure":
                        "on the departure day",
                    "i18n::intelligent-search-flight-hotel.passengers":
                        "Travellers",
                    "i18n::intelligent-search-flight-hotel.adults":
                        "Adults",
                    "i18n::intelligent-search-flight-hotel.18-years-and-more":
                        "Over 18 years ",
                    "i18n::intelligent-search-flight-hotel.children":
                        "Children",
                    "i18n::intelligent-search-flight-hotel.0-17-years":
                        "0-17 years",
                    "i18n::intelligent-search-flight-hotel.origin": "From",
                    "i18n::intelligent-search-flight-hotel.where-are-you-going-from":
                        "Where from?",
                    "i18n::intelligent-search-flight-hotel.departure-required":
                        "The selection of a departure location is mandatory.",
                    "i18n::intelligent-search-flight-hotel.no-result":
                        "No results were found.",
                    "i18n::intelligent-search-flight-hotel.destination":
                        "To",
                    "i18n::intelligent-search-flight-hotel.where-are-you-going":
                        "Where to?",
                    "i18n::intelligent-search-flight-hotel.arrival-required":
                        "The selection of an arrival location is mandatory.",
                    "i18n::intelligent-search-common.search": "Search",
                    "i18n::intelligent-search-city-break.age-of-the-child":
                        "Age of the child",
                    "i18n::intelligent-search-city-break.on-the-day-of-departure":
                        "on the departure day",
                    "i18n::intelligent-search-city-break.passengers":
                        "Travellers",
                    "i18n::intelligent-search-city-break.adults": "Adults",
                    "i18n::intelligent-search-city-break.18-years-and-more":
                        "Over 18 years",
                    "i18n::intelligent-search-city-break.children":
                        "Children",
                    "i18n::intelligent-search-city-break.0-17-years":
                        "0-17 years",
                    "i18n::intelligent-search-common.date-required":
                        "It is required to select a travel period.",
                    "i18n::intelligent-search-common.date-required-departure":
                        "It is required to select a departure date.",
                    "i18n::intelligent-search-city-break.origin": "From",
                    "i18n::intelligent-search-city-break.where-are-you-going-from":
                        "Where from?",
                    "i18n::intelligent-search-city-break.departure-required":
                        "The departure is required.",
                    "i18n::intelligent-search-city-break.no-result":
                        "No results found",
                    "i18n::intelligent-search-city-break.destination": "To",
                    "i18n::intelligent-search-city-break.where-are-you-going":
                        "Where to?",
                    "i18n::intelligent-search-city-break.arrival-required":
                        "Please enter a destination, it\u0027s mandatory!",
                    "i18n::intelligent-search-city-break.loading":
                        "Loading...",
                    "i18n::intelligent-search-city-break.search": "Search",
                    "i18n::intelligent-search-flight.count-passengers":
                        "@count Passengers",
                    "i18n::intelligent-search-flight-hotel.count-passengers":
                        "@count Travellers",
                    "i18n::intelligent-search-city-break.count-passengers":
                        "@count Travellers",
                    "i18n::intelligent-search-car-rental.search": "Search",
                    "i18n::intelligent-search-car-rental.pickup":
                        "Pick-up location",
                    "i18n::intelligent-search-car-rental.where-are-you-going-from":
                        "Pick-up location",
                    "i18n::intelligent-search-car-rental.pickup-required":
                        "Please select a pick-up location",
                    "i18n::intelligent-search-car-rental.no-result":
                        "No results found",
                    "i18n::intelligent-search-car-rental.return":
                        "Drop-off location",
                    "i18n::intelligent-search-car-rental.where-are-you-going-to":
                        "Drop-off location",
                    "i18n::intelligent-search-car-rental.return-required":
                        "Please select a drop-off location",
                    "i18n::intelligent-search-car-rental.age":
                        "Driver\u0027s age",
                    "i18n::intelligent-search-car-rental.age-required":
                        "The age of the driver is mandatory to continue",
                    "i18n::intelligent-search-car-rental.min-age":
                        "The driver should be at least 18 years old",
                    "i18n::intelligent-search-car-rental.date-required":
                        "Please select dates",
                    "i18n::intelligent-search-car-rental.date-required-pickup":
                        "Please select a pick-up date",
                    "i18n::intelligent-search-car-rental.date-required-return":
                        "Please select a drop-off date",
                    "i18n::intelligent-search-car-rental.pickup-placeholder":
                        "Pick-up date",
                    "i18n::intelligent-search-car-rental.pickup-date":
                        "Pick-up date",
                    "i18n::intelligent-search-car-rental.return-placeholder":
                        "Drop-off date",
                    "i18n::intelligent-search-car-rental.return-date":
                        "Drop-off date",
                    "i18n::intelligent-search-flight.toggle-round-trip":
                        "Round trip",
                    "i18n::intelligent-search-flight.toggle-one-way":
                        "One-way",
                    "i18n::intelligent-search-flight.umnr-image-link":
                        "\/sites\/default\/files\/styles\/slide_image_l_1025px_\/public\/2017-09\/LG_Brother_and_sister_web_0.jpg?itok=Qpb8B0e9",
                    "i18n::intelligent-search-flight.umnr-image-alt":
                        "Unaccompanied minor",
                    "i18n::intelligent-search-flight.umnr-title":
                        "Unaccompanied minor booking",
                    "i18n::intelligent-search-flight.umnr-text":
                        "You are about to book for an unaccompanied minor. Luxair is offering this service to children from 5 to 11 years. Please also note the current entry regulations for your destination.",
                    "i18n::intelligent-search-flight.umnr-more-info-link":
                        "https:\/\/www.luxair.lu\/en\/offers\/unaccompanied-children-and-minors",
                    "i18n::intelligent-search-flight.umnr-more-info-label":
                        "Learn more",
                    "i18n::intelligent-search-flight.umnr-cancel-label":
                        "Refine my search",
                    "i18n::intelligent-search-flight.umnr-confirm-label":
                        "Continue",
                    "i18n::intelligent-search-common.children-birthdate-range-error":
                        "Enter a valid child birthdate, must be of age 5 to 11 at the day of departure.",
                    "i18n::intelligent-search-flight.birthdate-of-the-child":
                        "Birthdate",
                    "i18n::intelligent-search-flight.minimum-passenger-error":
                        "Please add at least one passenger",
                    "i18n::intelligent-search-flight.maximum-passenger-error":
                        "It is not possible to book for more than 9 passengers (excluding infants) online. View more on \u003Ca href=\u0022https:\/\/www.luxair.lu\/en\/information\/group-offers\u0022\u003EGroup offers info page\u003C\/a\u003E.",
                    "i18n::intelligent-search-flight-hotel.maximum-passenger-error":
                        "It is not possible to book for more than 6 adults and 4 children online. View more on \u003Ca href=\u0022https:\/\/www.luxairtours.lu\/en\/information\/group-travel\u0022\u003EGroup travel info page\u003C\/a\u003E.",
                    "i18n::intelligent-search-city-break.maximum-passenger-error":
                        "It is not possible to book for more than 6 adults and 4 children online. View more on \u003Ca href=\u0022https:\/\/www.luxairtours.lu\/en\/information\/group-travel\u0022\u003EGroup travel info page\u003C\/a\u003E.",
                    "i18n::intelligent-search-flight.students-image-link":
                        "\/sites\/default\/files\/student_fare_2020.jpg",
                    "i18n::intelligent-search-flight.students-image-alt":
                        "Students",
                    "i18n::intelligent-search-flight.students-title":
                        "Student Special at 199\u20ac, return flight",
                    "i18n::intelligent-search-flight.students-text":
                        "If you are a student and between 18 and 27 old, Luxair offers you a special fare at 199\u20ac, return flight. Key benefits: Best availability - even until shortly before flight departure, 23kg of baggage included, and you can change your travel dates if needed. ",
                    "i18n::intelligent-search-flight.students-more-info-label":
                        " ",
                    "i18n::intelligent-search-flight.students-cancel-label":
                        "Refine my search",
                    "i18n::intelligent-search-flight.students-confirm-label":
                        "Confirm",
                    "i18n::intelligent-search-common.birthdate-picker.placeholder.day":
                        "dd",
                    "i18n::intelligent-search-common.birthdate-picker.placeholder.month":
                        "mm",
                    "i18n::intelligent-search-common.birthdate-picker.placeholder.year":
                        "yyyy",
                    "i18n::intelligent-search-flight.student-passenger-picker-info-message":
                        "Are you between 12 and 24 years? Discover our special fare. \u003Ca target=\u0022_blank\u0022 href=\u0022en\/offers\/new-luxair-young-traveller-special\u0022\u003EClick here\u003C\/a\u003E",
                    "i18n::intelligent-search-flight.students-exclusive-error":
                        "Students cannot be booked together with other passenger types.",
                    "i18n::intelligent-search-flight.students": "Student",
                    "i18n::intelligent-search-flight.students-years":
                        "18-27 years",
                    "i18n::intelligent-search-flight.umnr.age-rules":
                        "Service as of 5 years",
                    "i18n::web-contact-form.traveller-booking-reference-name-informations":
                        "Enter the \u003Cb\u003Elast name\u003C\/b\u003E as it appears on the booking confirmation or e-ticket.",
                    "i18n::intelligent-search-flight-hotel.no-results-for-your-settings":
                        "We apologize, we were not able to find availabilities for your search. Do you want to book \u003Ca target=\u0022_blank\u0022 href=\u0022https:\/\/booking.luxairtours.lu\/?sortBy=Priority\u0026sortOrder=desc\u0026lang=en\u0022\u003Emultiple rooms\u003C\/a\u003E?",
                    "i18n::intelligent-search-flight-hotel.failed-to-load-please-retry-later":
                        "The service is currently unavailable, please try again later.",
                    "i18n::intelligent-search-flight.tab-histogram-not-available-switch-tabs":
                        "See the flights calendar",
                    "i18n::web-contact-form.traveller-booking-reference-dialog-reset-title":
                        "Do you want to change the booking reference?",
                    "i18n::web-contact-form.traveller-booking-reference-dialog-reset-message":
                        "By confirming this action, you will lose some data that has already been entered.",
                    "i18n::web-contact-form.traveller-booking-reference-dialog-reset-cancel":
                        "Cancel",
                    "i18n::web-contact-form.traveller-booking-reference-dialog-reset-approve":
                        "Confirm",
                    "i18n::intelligent-search-flight-hotel.required-field":
                        "This field is required",
                    "i18n::intelligent-search-flight-hotel.min-child-age-error":
                        "The age of the child must be between 0 and 17 years old.",
                    "i18n::intelligent-search-flight-hotel.max-child-age-error":
                        "The age of the child must be between 0 and 17 years old.",
                    "i18n::intelligent-search-flight-hotel.minimum-passenger-error":
                        "At least 1 passenger is required",
                    "i18n::intelligent-search-flight-hotel.room.label":
                        "Room",
                    "i18n::intelligent-search-flight-hotel.room.group-booking-message":
                        "For travelling in groups of 10 or more travellers, please <NAME_EMAIL>.",
                    "i18n::intelligent-search-flight-hotel.room.group-booking-url":
                        "https:\/\/www.luxairtours.lu\/en\/information\/group-travel",
                    "i18n::intelligent-search-flight-hotel.room.group-message-link-label":
                        "More information",
                    "i18n::intelligent-search-flight-hotel.room.add":
                        "Add room",
                    "i18n::intelligent-search-flight-hotel.room.max-travelers":
                        "It is not possible to book online for more than 9 people.",
                    "i18n::intelligent-search-flight-hotel.room.max-room-number":
                        "It\u0027s currently not possible to book more than 3 rooms in the same booking.",
                    "i18n::destination-overview.see-all": "View all",
                    "i18n::destination-overview.category-list-label":
                        "Select a theme",
                    "i18n::destination-overview.discover-more": "View more",
                    "i18n::destination-overview.luxairtours-destinations":
                        " ",
                    "i18n::destination-overview.luxair-destinations": " ",
                    "i18n::destination-overview.country-list-title":
                        "Discover our destinations!",
                    "i18n::destination-overview.list-by-country":
                        "List by country",
                    "i18n::destination-overview.inspiration-by-themes":
                        "Inspiration by themes",
                    "i18n::luxair-destination.facts.currency": "Currency:",
                    "i18n::luxair-destination.facts.emergencyNumbers":
                        "Emergency numbers:",
                    "i18n::luxair-destination.facts.newspapers":
                        "Newspapers:",
                    "i18n::luxair-destination.facts.openingHours":
                        "Opening hours:",
                    "i18n::luxair-destination.facts.population":
                        "Population:",
                    "i18n::luxair-destination.facts.touristInformation":
                        "Tourist information:",
                    "i18n::luxair-destination.facts.www": "Website:",
                    "i18n::luxair-destination.useful-information":
                        "Useful information",
                    "i18n::luxair-destination.meta.address": "Address:",
                    "i18n::luxair-destination.meta.openinghours":
                        "Opening hours:",
                    "i18n::luxair-destination.meta.phone": "Phone number:",
                    "i18n::luxair-destination.meta.subway":
                        "Public transport:",
                    "i18n::luxair-destination.meta.tickets": "Tickets:",
                    "i18n::luxair-destination.meta.www": "Website:",
                    "i18n::luxair-destination.meta.moreinfo":
                        "More information",
                    "i18n::luxair-destination.discover-more": "Load more",
                    "i18n::luxair-destination.download-guide":
                        "Download the guide",
                    "i18n::luxair-destination.read-facts": "Learn more",
                    "i18n::luxair-destination.practical-information":
                        "What to see and what to do?",
                    "i18n::destination-overview.categories.see-all":
                        "View all",
                    "i18n::destination-overview.is-new": "New",
                    "i18n::web-contact-form.traveller-step-voucher-entitled-voucher-for-all":
                        "I am entitled to request a refund on behalf of all passengers.",
                    "i18n::web-contact-form.traveller-step-voucher-selection-for-me":
                        "Only for me",
                    "i18n::web-contact-form.traveller-step-voucher-selection-for-me-description":
                        "One voucher for yourself will be issued",
                    "i18n::web-contact-form.traveller-step-voucher-selection-for-all":
                        "For all passengers",
                    "i18n::web-contact-form.traveller-step-voucher-selection-for-all-description":
                        "A voucher for the entire reservation will be issued.",
                    "i18n::web-contact-form.traveller-step-voucher-terms-and-conditions-text":
                        "I have read and accept the ",
                    "i18n::web-contact-form.traveller-step-voucher-terms-and-condition-text-link":
                        "terms and conditions of use of the compensation voucher",
                    "i18n::web-contact-form.traveller-step-voucher-advertisement":
                        "If your flight is cancelled or delayed or if you are unable to board the flight you have booked, the operating air carrier is obliged to respect your rights under \u003Ca href=\u0022https:\/\/www.luxair.lu\/en\/information\/passenger-rights\u0022 target=\u0022_blank\u0022\u003EEuropean Regulation (EC) No 261\/2004\u003C\/a\u003E of 11 February 2004, which entered into force on 17 February 2005.\r\n\u003Cbr\u003E\u003Cbr\u003E\r\nWe will carefully examine your request. If you are eligible for compensation, you can choose between:",
                    "i18n::web-contact-form.traveller-step-voucher-selection-voucher":
                        "A compensation voucher (non-refundable)",
                    "i18n::web-contact-form.traveller-step-voucher-selection-voucher-description":
                        "If you choose this option, the compensation is increased up to 200\u20ac per person.",
                    "i18n::web-contact-form.traveller-step-voucher-selection-cash":
                        "A compensation by bank transfer",
                    "i18n::web-contact-form.traveller-step-voucher-selection-cash-description":
                        "No increase will be applied.",
                    "i18n::web-contact-form.traveller-step-voucher-terms-and-condition-text-link-href":
                        "https:\/\/www.luxair.lu\/en\/information\/compensation-vouchers-terms-and-conditions",
                    "i18n::web-contact-form.traveller-step-voucher-selection-voucher-informations":
                        "The amounts of the compensation voucher are as follows:\u003Cbr\u003E\u003Cbr\u003E\r\nFor a flight of up to 1,500 km, you will receive EUR 350 instead of EUR 250.\u003Cbr\u003E\r\nFor a flight from 1,500 km to 3,500 km, you will receive EUR 500 instead of EUR 400.\u003Cbr\u003E\r\nFor a flight of more than 3,500 km, you will receive EUR 800 instead of EUR 600.",
                    "i18n::intelligent-search-anywhere-cant-decide":
                        "Can\u2019t decide where to go?",
                    "i18n::intelligent-search-anywhere-description":
                        "Explore our destinations",
                    "i18n::intelligent-search-anywhere": "Anywhere",
                    "i18n::intelligent-search-flight.explore": "Explore",
                    "i18n::destination-overview.price.from": "From",
                    "i18n::destination-overview.price.no-price":
                        "No offers available",
                    "i18n::intelligent-search-common.multicity.date-precedence-error":
                        "The return date cannot be earlier than the departure date.",
                    "i18n::intelligent-search-flight.multicity.no-umnr-error":
                        "Non-accompanied minors are not allowed in multi-city booking.",
                    "i18n::intelligent-search-flight.multicity.segment-1":
                        "Flight 1",
                    "i18n::intelligent-search-flight.multicity.segment-2":
                        "Flight 2",
                    "i18n::intelligent-search-flight.multicity.distinct-route-error":
                        "Both flight segments must have different departure or destination airports.",
                    "i18n::intelligent-search-flight.toggle-multi-city":
                        "Multi-City",
                    "i18n::stage-teaser.starting-from-price": "Price from",
                    "i18n::stage-teaser.product.flight": "Flight",
                    "i18n::stage-teaser.product.package": "Package",
                    "i18n::stage-teaser.night": "night ",
                    "i18n::stage-teaser.nights": "nights",
                    "i18n::intelligent-search-flight-hotel.room.labelPlural":
                        "Rooms",
                    "i18n::intelligent-search-flight-hotel.child": "Child",
                    "i18n::intelligent-search-flight-hotel.adult": "Adult",
                    "i18n::intelligent-search-flight.youth": "Youth",
                    "i18n::intelligent-search-flight.infant": "Infant",
                },
            },
            pluralFormula: { 0: 0, 1: 0, default: 1 },
        };
        window.Drupal = { behaviors: {}, locale: {} };

        (function (
            Drupal,
            drupalSettings,
            drupalTranslations,
            console,
            Proxy,
            Reflect
        ) {
            Drupal.throwError = function (error) {
                setTimeout(function () {
                    throw error;
                }, 0);
            };

            Drupal.attachBehaviors = function (context, settings) {
                context = context || document;
                settings = settings || drupalSettings;
                var behaviors = Drupal.behaviors;

                Object.keys(behaviors || {}).forEach(function (i) {
                    if (typeof behaviors[i].attach === "function") {
                        try {
                            behaviors[i].attach(context, settings);
                        } catch (e) {
                            Drupal.throwError(e);
                        }
                    }
                });
            };

            Drupal.detachBehaviors = function (context, settings, trigger) {
                context = context || document;
                settings = settings || drupalSettings;
                trigger = trigger || "unload";
                var behaviors = Drupal.behaviors;

                Object.keys(behaviors || {}).forEach(function (i) {
                    if (typeof behaviors[i].detach === "function") {
                        try {
                            behaviors[i].detach(context, settings, trigger);
                        } catch (e) {
                            Drupal.throwError(e);
                        }
                    }
                });
            };

            Drupal.checkPlain = function (str) {
                str = str
                    .toString()
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#39;");
                return str;
            };

            Drupal.formatString = function (str, args) {
                var processedArgs = {};

                Object.keys(args || {}).forEach(function (key) {
                    switch (key.charAt(0)) {
                        case "@":
                            processedArgs[key] = Drupal.checkPlain(
                                args[key]
                            );
                            break;

                        case "!":
                            processedArgs[key] = args[key];
                            break;

                        default:
                            processedArgs[key] = Drupal.theme(
                                "placeholder",
                                args[key]
                            );
                            break;
                    }
                });

                return Drupal.stringReplace(str, processedArgs, null);
            };

            Drupal.stringReplace = function (str, args, keys) {
                if (str.length === 0) {
                    return str;
                }

                if (!Array.isArray(keys)) {
                    keys = Object.keys(args || {});

                    keys.sort(function (a, b) {
                        return a.length - b.length;
                    });
                }

                if (keys.length === 0) {
                    return str;
                }

                var key = keys.pop();
                var fragments = str.split(key);

                if (keys.length) {
                    for (var i = 0; i < fragments.length; i++) {
                        fragments[i] = Drupal.stringReplace(
                            fragments[i],
                            args,
                            keys.slice(0)
                        );
                    }
                }

                return fragments.join(args[key]);
            };

            Drupal.t = function (str, args, options) {
                options = options || {};
                options.context = options.context || "";

                if (
                    typeof drupalTranslations !== "undefined" &&
                    drupalTranslations.strings &&
                    drupalTranslations.strings[options.context] &&
                    drupalTranslations.strings[options.context][str]
                ) {
                    str = drupalTranslations.strings[options.context][str];
                }

                if (args) {
                    str = Drupal.formatString(str, args);
                }
                return str;
            };

            Drupal.url = function (path) {
                return (
                    drupalSettings.path.baseUrl +
                    drupalSettings.path.pathPrefix +
                    path
                );
            };

            Drupal.url.toAbsolute = function (url) {
                var urlParsingNode = document.createElement("a");

                try {
                    url = decodeURIComponent(url);
                } catch (e) { }

                urlParsingNode.setAttribute("href", url);

                return urlParsingNode.cloneNode(false).href;
            };

            Drupal.url.isLocal = function (url) {
                var absoluteUrl = Drupal.url.toAbsolute(url);
                var protocol = window.location.protocol;

                if (
                    protocol === "http:" &&
                    absoluteUrl.indexOf("https:") === 0
                ) {
                    protocol = "https:";
                }
                var baseUrl =
                    protocol +
                    "//" +
                    window.location.host +
                    drupalSettings.path.baseUrl.slice(0, -1);

                try {
                    absoluteUrl = decodeURIComponent(absoluteUrl);
                } catch (e) { }
                try {
                    baseUrl = decodeURIComponent(baseUrl);
                } catch (e) { }

                return (
                    absoluteUrl === baseUrl ||
                    absoluteUrl.indexOf(baseUrl + "/") === 0
                );
            };

            Drupal.formatPlural = function (
                count,
                singular,
                plural,
                args,
                options
            ) {
                args = args || {};
                args["@count"] = count;

                var pluralDelimiter = drupalSettings.pluralDelimiter;
                var translations = Drupal.t(
                    singular + pluralDelimiter + plural,
                    args,
                    options
                ).split(pluralDelimiter);
                var index = 0;

                if (
                    typeof drupalTranslations !== "undefined" &&
                    drupalTranslations.pluralFormula
                ) {
                    index =
                        count in drupalTranslations.pluralFormula
                            ? drupalTranslations.pluralFormula[count]
                            : drupalTranslations.pluralFormula.default;
                } else if (args["@count"] !== 1) {
                    index = 1;
                }

                return translations[index];
            };

            Drupal.encodePath = function (item) {
                return window.encodeURIComponent(item).replace(/%2F/g, "/");
            };

            Drupal.deprecationError = function (_ref) {
                var message = _ref.message;

                if (
                    drupalSettings.suppressDeprecationErrors === false &&
                    typeof console !== "undefined" &&
                    console.warn
                ) {
                    console.warn("[Deprecation] " + message);
                }
            };

            Drupal.deprecatedProperty = function (_ref2) {
                var target = _ref2.target,
                    deprecatedProperty = _ref2.deprecatedProperty,
                    message = _ref2.message;

                if (!Proxy || !Reflect) {
                    return target;
                }

                return new Proxy(target, {
                    get: function get(target, key) {
                        for (
                            var _len = arguments.length,
                            rest = Array(_len > 2 ? _len - 2 : 0),
                            _key = 2;
                            _key < _len;
                            _key++
                        ) {
                            rest[_key - 2] = arguments[_key];
                        }

                        if (key === deprecatedProperty) {
                            Drupal.deprecationError({ message: message });
                        }
                        return Reflect.get.apply(
                            Reflect,
                            [target, key].concat(rest)
                        );
                    },
                });
            };

            Drupal.theme = function (func) {
                if (func in Drupal.theme) {
                    var _Drupal$theme;

                    for (
                        var _len2 = arguments.length,
                        args = Array(_len2 > 1 ? _len2 - 1 : 0),
                        _key2 = 1;
                        _key2 < _len2;
                        _key2++
                    ) {
                        args[_key2 - 1] = arguments[_key2];
                    }

                    return (_Drupal$theme = Drupal.theme)[func].apply(
                        _Drupal$theme,
                        args
                    );
                }
            };

            Drupal.theme.placeholder = function (str) {
                return (
                    '<em class="placeholder">' +
                    Drupal.checkPlain(str) +
                    "</em>"
                );
            };
        })(
            window.Drupal,
            window.drupalSettings,
            window.drupalTranslations,
            window.console,
            window.Proxy,
            window.Reflect
        );
        /**
         * DO NOT EDIT THIS FILE.
         * See the following change record for more information,
         * https://www.drupal.org/node/2815083
         * @preserve
         **/

        if (window.jQuery) {
            jQuery.noConflict();
        }

        document.documentElement.className += " js";

        (function (Drupal, drupalSettings) {
            var domReady = function domReady(callback) {
                if (document.readyState !== "loading") {
                    callback();
                } else {
                    var listener = function listener() {
                        callback();
                        document.removeEventListener(
                            "DOMContentLoaded",
                            listener
                        );
                    };
                    document.addEventListener("DOMContentLoaded", listener);
                }
            };

            domReady(function () {
                Drupal.attachBehaviors(document, drupalSettings);
            });
        })(Drupal, window.drupalSettings);


    </script>
    <script>
        document.addEventListener(
            'aemEvent',
            e => {
                console.log('e', e.detail);
            },
            false
        );


    </script>
    <script>
        luxairPro = {
            isLuxairProUserLoggedIn: () => !!sessionStorage.getItem('isLuxairProUserLoggedIn')
        }

    </script>
    <script type="text/javascript">
        // list of paths where we hide the cookie consent popup
        var cookie_consent_blacklist_paths = [
            '/en/information-light/cookie-policy',
            '/fr/information-light/politique-de-cookies',
            '/de/informationen-light/cookie-richtlinie',
            '/pt/informacao-leve/politica-de-cookies',
            '/it/informazioni-leggero/politica-dei-cookie',
            '/es/informacion-ligero/politica-de-cookies',
            '/en/information/security-and-privacy-policy',
            '/fr/information/politique-de-securite-et-de-confidentialite',
            '/de/informationen/datensicherheit-und-datenschutzrichtlinie',
            '/pt/informacao/protecao-dos-dados-pessoais',
            '/it/informazioni/protezione-dei-dati-personali',
            '/es/informacion/proteccion-de-datos-personales'
        ];
        // select language rom url, default ; 'en'
        var selectedLanguage;
        try {
            selectedLanguage = document.cookie.split(';').map(function f(el) {
                return el.trim();
            }).filter(function f(i) {
                return i.startsWith('preferedLanguage');
            })[0].split('=')[1] || 'en';
        } catch (e) {
            selectedLanguage = 'en';
        }
        privacyPolicyURLs = {
            'en': 'https://www.luxair.lu/en/information-light/cookie-policy',
            'fr': 'https://www.luxair.lu/fr/information-light/politique-de-cookies',
            'de': 'https://www.luxair.lu/de/informationen-light/cookie-richtlinie',
            'pt': 'https://www.luxair.lu/pt/informacao-leve/politica-de-cookies',
            'it': 'https://www.luxair.lu/it/informazioni-leggero/politica-dei-cookie',
            'es': 'https://www.luxair.lu/es/informacion-ligero/politica-de-cookies'
        };
        if (cookie_consent_blacklist_paths.indexOf(window.location.pathname) !== -1) {
            window.didomiConfig = {
                notice: {
                    enable: false
                }
            };
        } else {
            window.didomiConfig = {
                sdkPath: 'https://privacy.luxair.lu/',
                apiPath: 'https://privacy.luxair.lu/api',
                app: {
                    privacyPolicyURL: privacyPolicyURLs[selectedLanguage]
                },
                languages: {
                    enabled: [selectedLanguage],
                    default: selectedLanguage
                }
            };
        }
        (function () {
            (function (e) {
                var r = document.createElement("link");
                r.rel = "preconnect"; r.as = "script";
                var t = document.createElement("link");
                t.rel = "dns-prefetch";
                t.as = "script";
                var n = document.createElement("script");
                n.id = "spcloader";
                n.type = "text/javascript"; n["async"] = true;
                n.charset = "utf-8";
                var o = window.didomiConfig.sdkPath + e + "/loader.js?target=" + document.location.hostname;
                if (window.didomiConfig && window.didomiConfig.user) {
                    var i = window.didomiConfig.user;
                    var a = i.country;
                    var c = i.region;
                    if (a) {
                        o = o + "&country=" + a;
                        if (c) {
                            o = o + "&region=" + c
                        }
                    }
                }
                r.href = window.didomiConfig.sdkPath;
                t.href = window.didomiConfig.sdkPath;
                n.src = o;
                var d = document.getElementsByTagName("script")[0];
                d.parentNode.insertBefore(r, d);
                d.parentNode.insertBefore(t, d);
                d.parentNode.insertBefore(n, d)
            })("d1247a6b-5e95-46d1-8aa4-84a358da11d2")
        })();
    </script>

    <!-- <script src="https://www.luxair.lu/sites/default/files/js/js_KJG1MjIdI92zLPePF7D5pZVBH0kyfh-7iupLYvRemPk.js"></script> -->
    <script src="https://dxp-assets.luxair.lu/0.47.1-31/en/runtime.js"></script>
    <script src="https://dxp-assets.luxair.lu/0.47.1-31/en/polyfills.js"></script>
    <script src="https://dxp-assets.luxair.lu/0.47.1-31/en/main.js"></script>

</body>

</html>
