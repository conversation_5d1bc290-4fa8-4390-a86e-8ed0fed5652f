{% for key, item in links %}
  {% set wrapper_attributes = create_attribute() %}
  {% set nav_link_class = 'nav__link--language-' ~ key %}
  {% set nav_link_active_class = (key == current) ? 'nav__link--language--active' : '' %}
  {% set wrapper_attributes = wrapper_attributes.addClass('nav__link', 'nav__link--language', nav_link_active_class, nav_link_class, 'italic', 'hidden-on-desktop') %}
  <div{{ wrapper_attributes }}>
    <a href="{{ item.link['#url'] }}" class="nav__link__name language-{{ key }}">
      <img src="/modules/custom/luxair_styleguide/build/images/icons/flags/flag-{{ key }}.svg" alt="{{ key }}" loading="lazy">
      <span>{{ item.text }}</span>
    </a>
  </div>
{% endfor %}
