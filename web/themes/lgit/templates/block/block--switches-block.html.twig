{#
/**
 * @file
 * LGIT theme implementation to display a block.
 *
 * Available variables:
 * - plugin_id: The ID of the block implementation.
 * - label: The configured label of the block if visible.
 * - configuration: A list of the block's configuration values.
 *   - label: The configured label for the block.
 *   - label_display: The display settings for the label.
 *   - provider: The module or other provider that provided this block plugin.
 *   - Block plugin specific settings will also be stored here.
 * - content: The content of this block.
 * - attributes: array of HTML attributes populated by modules, intended to
 *   be added to the main container tag of this template.
 *   - id: A valid HTML ID and guaranteed unique.
 * - title_attributes: Same as attributes, except applied to the main title
 *   tag that appears in the template.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 *
 * @see template_preprocess_block()
 *
 * @ingroup themeable
 */
#}
<div class="switches" data-ng-class="{ active : isBurgerActive() }">
    {% if content.myluxair_block_output %}
      {{ content.myluxair_block_output }}
    {% endif %}

    {% if content.contact_form %}
        {{ content.contact_form }}
    {% endif %}

    <div class="switches_countrylanguage hidden-on-mobile">
        <div class="switches__contact">
            <div class="switches__phone">
                <div>
                    <a class='tooltip__trigger' id="tooltipContactPhone" href='#'>
                        <span class="icon icon--phone" />
                    </a>
                    <lg-tooltip data-trigger-id="tooltipContactPhone">
                        <div class="tooltip">
                            <div class="tooltip__content">
                                <p>{{ 'phone_box.phone_display' | t }}<br>{{ 'phone_box.opening_hours' | t }}</p>
                                <a href="tel:{{ 'phone_box.phone_number' | t }}" class="button__inner">{{ 'Call now!'|t }}</a>
                            </div>
                        </div>
                    </lg-tooltip>
                </div>
            </div>
            <div class="switches__mail">
                <div>
                    <a class='tooltip__trigger' id="" href="{{ 'phone_box.contact_form_link' | t }}">
                        <span class="icon icon--mail" />
                    </a>
                </div>
            </div>
        </div>

        {{ content.country_switch }}

        <div class="dropdown"
             data-ng-controller="lgDropdownController"
             data-ng-click="onClick($event)"
             data-ng-class="{ active : isActive() }">
            {{ content.language_selector }}
        </div>
    </div>
    <div class="switches__burger" data-ng-click="onBurgerClick()"></div>
</div>
