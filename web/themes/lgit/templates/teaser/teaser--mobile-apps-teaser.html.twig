{#
/**
 * @file teaser--single-offer-fs-illustrated.html.twig
 * Single offer theme implementation to present Teaser data.
 *
 * This template is used when viewing Teaser pages.
 *
 *
 * Available variables:
 * - content: A list of content items. Use 'content' to print all content, or
 * - attributes: HTML attributes for the container element.
 * - teaser_renderer: Teaser renderer object for wrapping service calls and
 *   overrides.
 *
 * @see template_preprocess_teaser()
 *
 * @ingroup themeable
 */
#}

{% set android = teaser_renderer.deepLink(0) %}
{% set android_attr = teaser_renderer.hyperlinkAttributes(android) %}
{% set ios = teaser_renderer.deepLink(1) %}
{% set ios_attr = teaser_renderer.hyperlinkAttributes(ios) %}
<section class="topicBlock">
	<h3>{{ teaser_renderer.headline(0) }}</h3>
	<p>{{ teaser_renderer.teasertext(0) }}</p>
	<div class="container-mobile-apps">
		<div class="grid grid--center grid--collapsedVertical">
			{% if android is not empty %}
				<div class="grid__cell grid__cell--xs-12 grid__cell--d-6" href="{{ android }}">
					<a class="mobileApps__icon mobileApps__icon--android" href="{{ android }}" {{ android_attr }}></a>
				</div>
			{% endif %}

			{% if ios is not empty %}
				<div class="grid__cell grid__cell--xs-12 grid__cell--d-6" href="{{ ios }}">
					<a class="mobileApps__icon mobileApps__icon--ios" href="{{ ios }}" {{ ios_attr }}></a>
				</div>
			{% endif %}
		</div>
	</div>
</section>
<style>
	@media(min-width: 1200px) {
		.container-mobile-apps {
			max-width: 1140px;
		}
	}

	@media(min-width: 992px) {
		.container-mobile-apps {
			max-width: 960px;
		}
	}

	@media(min-width: 768px) {
		.container-mobile-apps {
			max-width: 720px;
		}
	}

	@media(min-width: 576px) {
		.container-mobile-apps {
			max-width: 540px;
		}
	}
	.container-mobile-apps {
		min-width: 100%;
	}
</style>
