{{ attach_library('luxair_intelligent_search/dist') }}
<is-intelligent-search-tabs
    {% if content.field_paragraph_identifier.0 %}
        id="{{ content.field_paragraph_identifier.0 }}"
    {% endif %}
    active_tab_index="{{ content.field_default_active_tab_intel|render|striptags|trim|default('0') }}">
    {% if (is_flight_teaser) %}
    <is-tab title="{{ flight_label }}" icon="airplane-tilted">
        {{ content.field_app_intel_flight_search }}
    </is-tab>
    {% endif %}
    {% if (is_travel_teaser) %}
    <is-tab title="{{ travel_label }}" icon="palm">
        {{ content.field_app_intel_package_search }}
    </is-tab>
    {% endif %}
    {% if is_city_break_teaser %}
    <is-tab title="{{ city_break_label }}" icon="city">
        {{ content.field_app_intel_citybreak_search }}
    </is-tab>
    {% endif %}
    {% if is_car_rental_teaser %}
    <is-tab title="{{ car_rental_label }}" icon="car">
        {{ content.field_app_intel_carrental_search }}
    </is-tab>
    {% endif %}
</is-intelligent-search-tabs>
