{% block paragraph %}
	<!-- Used for static generation:
	    {% for info_page_url in info_page_urls %}
	        <a href="{{ info_page_url }}" rel="nofollow"></a>
	    {% endfor %}
	    -->
  <section class="paragraph_info_page" {% if content.field_paragraph_identifier.0 %} id="{{ content.field_paragraph_identifier.0 }}" {% endif %}>

    <style>
    :host
        {
      width: 100%;
      height: 100%;
    }

    @keyframes loadingPlaceholderAnimation {
      0% {
        background-position: 0 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0 50%;
      }
    }

    .loading-placeholder-content {
      position: relative;
      animation-duration: 2.5s;
      animation-fill-mode: forwards;
      animation-iteration-count: infinite;
      animation-name: loadingPlaceholderAnimation;
      animation-timing-function: ease-in-out;
      background: linear-gradient(to right, #ebebeb 16%, #dddddd 21%, #ebebeb 38%);
      background-size: 600%;
      height: 100%;
      border-radius: 4px;
    }

    .loading-placeholder-content.circle {
      border-radius: 50%;
    }


    .info-page-loading-container .section-title {
      height: 35px;
      width: 132px;
      margin-bottom: 16px;
    }

    .info-page-loading-container .skeleton-container {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      margin-bottom: 50px;
    }

    .skeleton-card {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 8px;
      min-width: 328px;
      height: 108px;
      border-radius: 10px;
      border: 1px solid #A3A3A3;
      padding: 16px 12px;
    }

    .info-card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 24px;
      width: 100%;
    }

    .info-card-icon-placeholder {
      width: 30px;
      height: 30px;
    }

    .info-card-title-placeholder {
      height: 20px;
      width: 128px;
    }

    .info-card-description-placeholder {
      width: 140px;
      height: 18px;
    }

    .info-card-description-first-line-placeholder-desktop-only,
    .info-card-description-second-line-placeholder-desktop-only {
      display: none;
    }

    @media(min-width: 768px) {
      .info-card-title-placeholder {
        height: 24px;
      }

      .info-card-description-placeholder {
        height: 21px;
        width: 224px;
        display: none;

      }

      .info-card-description-first-line-placeholder-desktop-only,
      .info-card-description-second-line-placeholder-desktop-only {
        display: block;
        flex-shrink: 0;
      }

      .info-card-description-first-line-placeholder-desktop-only {
        width: 224px;
        height: 21px;
      }

      .info-card-description-second-line-placeholder-desktop-only {
        width: 119px;
        height: 21px;
      }
    }
    @media(max-width: 768px) {
      .info-card-description-placeholder .loading-placeholder-content {
        display: none;
      }
      .info-page-loading-container .section-title .skeleton-card {
        max-height: 62px;
      }
    }
  </style>

  <div class="info-page-loading-container">
    {% for section in 1..3 %}
      <div class="section-title"></div>
      <div class="skeleton-container">
        {% for i in 1..4 %}
          <div class="skeleton-card">
            <div class="info-card-header">
              <div class="info-card-icon-placeholder">
                <div class="loading-placeholder-content circle"></div>
              </div>
              <div class="info-card-title-placeholder">
                <div class="loading-placeholder-content"></div>
              </div>
            </div>
            <div class="info-card-description-placeholder">
              <div class="loading-placeholder-content"></div>
            </div>
            <div class="info-card-description-first-line-placeholder-desktop-only">
              <div class="loading-placeholder-content"></div>
            </div>
            <div class="info-card-description-second-line-placeholder-desktop-only">
              <div class="loading-placeholder-content"></div>
            </div>
          </div>
        {% endfor %}
      </div>
    {% endfor %}
  </div>
  {% block content %}
    <info-page style="display: none;">{{ info_page_data|json_encode }}</info-page>
  {% endblock %}
</section>
{% endblock paragraph %}
