<?php

use Drupal\Core\Render\Element;
use Drupal\Core\Url;
use Drupal\file\Entity\File;
use Drupal\image\Entity\ImageStyle;
use Drupal\luxair_api_endpoint\Entity\LuxairApiEndpointEntity;

/**
 * Implements hook_preprocess_HOOK() for HTML document templates.
 */
function lgit_preprocess_html(&$variables)
{
  $request = \Drupal::request();
  $variables['attributes']['class'][] = 'luxair';
  $variables['attributes']['data-theme'] = $variables['site'] =
    \Drupal::service('theme.manager')->getActiveTheme()->getName();

  // Set the environment based on the DRUPAL_ENVIRONMENT environment variable.
  $variables['html_attributes']['data-luxair-environment'] = [
    'acc' => 'acceptance',
    'prd' => 'production',
    'tst' => 'testing',
  ][getenv('DRUPAL_ENVIRONMENT')] ?? NULL;

  $version_shell = \Drupal::config('lgit.dxp_assets')->get('dxp_assets_version');
  $variables['html_attributes']['data-dxp-assets-version'] = $version_shell;

  $version_myluxair = \Drupal::config('lgit.dxp_assets')->get('dxp_assets_myluxair_version');
  $variables['html_attributes']['data-dxp-assets-myluxair-version'] = $version_myluxair;

  $version_intelligent_search = \Drupal::config('lgit.dxp_assets')->get('dxp_assets_intelligent_search_version');
  $variables['html_attributes']['data-dxp-assets-intelligent-search-version'] = $version_intelligent_search;

  $langcode = \Drupal::languageManager()->getCurrentLanguage()->getId();
  $variables['#attached']['library'][] = "luxair/dxp_assets_{$langcode}";
  $variables['#attached']['drupalSettings']['host'] = $request->getSchemeAndHttpHost();
  $variables['#attached']['drupalSettings']['aemConfig'] = \Drupal::config('lgit.aem_config')->get('aemConfig');
  $variables['#attached']['drupalSettings']['apiKey'] = \Drupal::config('lgit.api_key')->get('apiKey');



  $variables['dxp_assets_url'] = \Drupal::config('luxair_api_endpoint.luxair_api_endpoint_entity.luxair_dxp_assets_url')
    ->get('uri');
  $variables['language'] = $langcode;
}

/**
 * Implements hook_preprocess_HOOK().
 *
 * Add the separate blocks available to the template.
 */
function lgit_preprocess_region__topic_blocks(&$variables)
{
  $variables['blocks'] = Element::children($variables['elements']);
}

/**
 * Implements hook_library_info_alter().
 */
function lgit_library_info_alter(&$libraries)
{

  $version_shell = \Drupal::config('lgit.dxp_assets')->get('dxp_assets_version');
  $version_myluxair = \Drupal::config('lgit.dxp_assets')->get('dxp_assets_myluxair_version');

  $dxp_assets_base_url = \Drupal::service('entity_type.manager')
    ->getStorage('luxair_api_endpoint_entity')
    ->load('luxair_dxp_assets_url')
    ->getUri();

  if (is_null($dxp_assets_base_url)) {
    return;
  }

  $host_and_base_path_shell = "{$dxp_assets_base_url}/{$version_shell}/";
  $host_and_base_path_myluxair = "{$dxp_assets_base_url}/myluxair/{$version_myluxair}/";

  $js_settings = [
    'type' => 'external',
    'attributes' => ['defer' => TRUE],
  ];

  $js_myluxair_settings = [
    'type' => 'external',
    'attributes' => ['defer' => TRUE, 'type' => 'module'],
  ];
  foreach (\Drupal::languageManager()->getLanguages() as $language) {
    $libraries['dxp_assets_' . $language->getId()] = [
      'js' => [
        "{$host_and_base_path_shell}{$language->getId()}/runtime.js" => $js_settings,
        "{$host_and_base_path_shell}{$language->getId()}/polyfills.js" => $js_settings,
        "{$host_and_base_path_shell}{$language->getId()}/main.js" => $js_settings,
      ],
      'css' => [
        'base' => [
          "{$host_and_base_path_shell}{$language->getId()}/styles.css" => [
            'type' => 'external',
          ],
        ]
      ],
      'dependencies' => ['core/drupalSettings'],
    ];
    $libraries['dxp_assets_myluxair_' . $language->getId()] = [
      'js' => [
        "{$host_and_base_path_myluxair}{$language->getId()}/vendor.js" => $js_myluxair_settings,
        "{$host_and_base_path_myluxair}{$language->getId()}/polyfills.js" => $js_myluxair_settings,
        "{$host_and_base_path_myluxair}{$language->getId()}/runtime.js" => $js_myluxair_settings,
        "{$host_and_base_path_myluxair}{$language->getId()}/main.js" => $js_myluxair_settings,
      ],
      'css' => [
        'base' => [
          "{$host_and_base_path_myluxair}{$language->getId()}/styles.css" => [
            'type' => 'external',
          ],
        ]
      ],
    ];
  }

  return $libraries;
}

/**
 * Remove the <div> container that is added to the views if the view is the luxairgroup_sites__block_sites
 * @param $variables
 * @param $hook
 */
function lgit_preprocess_views_view(&$variables, $hook)
{
  if ($hook === 'views_view__luxairgroup_sites__block_sites') {
    unset($variables['view_array']['#theme_wrappers'][0]);
  }
  // Make FAQ categories views header translatable
  if (isset($variables['id']) && $variables['display_id'] === 'faq_by_category_block') {
    $variables['header']['result']['#markup'] = Drupal::translation()
      ->formatPlural(
        count($variables['view']->result),
        '1 faq.result',
        '@count faq.results'
      );
  }
}

/**
 * Remove the <div> container that is added to the blocks if the block is the luxairgroup_sites-block_sites
 * @param $variables
 * @param $hook
 */
function lgit_preprocess_block(&$variables, $hook)
{
  if ($variables['plugin_id'] === 'views_block:luxairgroup_sites-block_sites') {
    unset($variables['content']['#theme_wrappers'][0]);
  }
}



/**
 * For the unformatted__luxairgroup_sites__block_sites which is shown on the top of main navigation
 * The blocks with images will be displayed in the first row and the ones without image in the second row
 *
 * Implements hook_preprocess_views_view_unformatted().
 */
function lgit_preprocess_views_view_unformatted(&$variables, $hook)
{
  if ($hook === 'views_view_unformatted__luxairgroup_sites__block_sites') {
    $rows = $variables['rows'];
    $customRows = [
      'rowsTop' => [],
      'rowsBottom' => [],
    ];
    foreach ($rows as $key => $val) {
      if (!$val['content']['#block_content']->field_image->isEmpty()) {
        $rowTarget = 'rowsTop';
      } else {
        $rowTarget = 'rowsBottom';
      }
      $customRows[$rowTarget][] = $val;
    }

    // This is used in the template to assign the proper css "col-X" class
    $colTopClass = (count($customRows['rowsTop']) >= 1) ? 12 / count($customRows['rowsTop']) : 12;
    $colBottomClass = (count($customRows['rowsBottom']) >= 1) ? 12 / count($customRows['rowsBottom']) : 12;

    $variables['rowsTop'] = [
      'rows' => $customRows['rowsTop'],
      'totalCols' => $colTopClass
    ];

    $variables['rowsBottom'] = [
      'rows' => $customRows['rowsBottom'],
      'totalCols' => $colBottomClass
    ];
  } elseif ($hook == 'views_view_unformatted__destination_list_megadropdown') {
    // Separate the destinations in two columns.
    $language = \Drupal::languageManager()->getCurrentLanguage();
    $total = count($variables['rows']);
    $half = ceil($total / 2);
    $left = [];
    $right = [];
    for ($i = 0; $i < $total; $i++) {
      // Show the translated name and url if available.
      if ($variables['rows'][$i]['content']['#row']->_entity->hasTranslation($language->getId())) {
        $entity = $variables['rows'][$i]['content']['#row']->_entity->getTranslation($language->getId());
      } else {
        $entity = $variables['rows'][$i]['content']['#row']->_entity;
      }
      $variables['rows'][$i]['content']['#row']->url = $variables['rows'][$i]['content']['#row']->_entity->url('canonical', ['language' => $language]);
      $variables['rows'][$i]['content']['#row']->destination_name = $entity->title->value;
      $i < $half ? $left[] = $variables['rows'][$i] : $right[] = $variables['rows'][$i];
    }
    $variables['rows'] = [$left, $right];
  }
}

/**
 * @param $variables
 * @param $hook
 *
 * Implements template_preprocess_image().
 */
function lgit_preprocess_image(&$variables, $hook)
{
  // Remove width and height attribute from images.
  unset($variables['attributes']['width']);
  unset($variables['attributes']['height']);
}

/**
 * Implements hook_page_attachments_alter().
 *
 * Provide a customized "viewport" meta tag.
 */
function lgit_page_attachments_alter(array &$attachments)
{

  foreach ($attachments['#attached']['html_head'] as $key => $attachment) {
    if ($attachment[1] == 'system_meta_generator') {
      unset($attachments['#attached']['html_head'][$key]);
    }
  }

  $viewport = array(
    '#type' => 'html_tag',
    '#tag' => 'meta',
    '#attributes' => array(
      'name' => 'viewport',
      'content' => 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0',
    ),
  );

  $attachments['#attached']['html_head'][] = [$viewport, 'viewport'];

  $manifest = array(array(
    'rel' => 'manifest',
    'href' => '/themes/lgit/manifest.json',
  ));

  $attachments['#attached']['html_head_link'][] = $manifest;
}

function lgit_preprocess_page(&$variables)
{

  $is_anonymous = \Drupal::currentUser()->isAnonymous();
  $variables['page']['b2blogoUrl'] = '';

  if (!$is_anonymous) {
    $membership_loader = \Drupal::service('group.membership_loader');
    $membership = $membership_loader->loadByUser(\Drupal::currentUser());
    if (!empty($membership[0])) {
      $group = $membership[0]->getGroup();
      $url = \Drupal::service('path_alias.manager')->getAliasByPath('/group/' . $group->id());
      $fid = $group->get('field_corporate_logo')->getValue('target_id')[0]['target_id'];
      $file = File::load($fid);

      $variables['page']['b2blogoUrl'] = ImageStyle::load('thumbnail')->buildUrl($file->getFileUri());
      $variables['page']['b2bPageUrl'] = $url;
    }
  }

  // Load the sdk of adobe experience platform ( should be move the gtm_tag project ?)
  $variables['#attached']['library'][] = 'lgit/aem';

  $request = \Drupal::request();


  unset($variables['#attached']['library']['luxair_stage_teasers/dist_luxair_stage_teasers']);
}

/**
 * Implements hook_theme_suggestions_HOOK_alter().
 */
function lgit_theme_suggestions_page_alter(array &$suggestions, array $variables)
{

  if ($node = \Drupal::routeMatch()->getParameter('node')) {
    if (gettype($node) == 'string') {
      $node = \Drupal\node\Entity\Node::load($node);
    }

    $content_type = $node->bundle();
    $suggestions[] = 'page__' . $content_type;
  }
}

function _lgitTheme_var($var_name, $new_val = NULL)
{
  $vars = &drupal_static(__FUNCTION__, array());

  // If a new value has been passed
  if ($new_val) {
    $vars[$var_name] = $new_val;
  }

  return isset($vars[$var_name]) ? $vars[$var_name] : NULL;
}

function lgit_preprocess_teaser(&$variables)
{

  $teaser_type = $variables['elements']['#teaser']->get('type')->__get('target_id');

  if ($teaser_type == 'offer_teaser_group') {

    drupal_static_reset();

    $offerTeaserGroupType = $variables['content']['field_style_switcher'][0]['#markup'];
    $theme_name = \Drupal::service('theme.manager')->getActiveTheme()->getName();
    $theme_style_suffix = '';
    $theme_style = '';
    $button_style = '';

    if ($theme_name == 'lgit') {
      $theme_style_suffix = '-lg';
    } elseif ($theme_name == 'luxair') {
      $theme_style_suffix = '-lt';
    }

    if ($offerTeaserGroupType == 'Default') {
      $theme_style = '';
      $button_style = 'price--inverse';
    }

    if ($offerTeaserGroupType == 'forceStyle') {
      $theme_style = 'force' . $theme_style_suffix;
      $button_style = 'price--inverse';
    }

    if ($offerTeaserGroupType == 'priceInline') {
      $theme_style = '';
      $button_style = 'price--inline';
    }

    if ($offerTeaserGroupType == 'priceInlineForceStyle') {
      $theme_style = 'force' . $theme_style_suffix;
      $button_style = 'price--inline';
    }

    $variables['theme_style'] = _lgitTheme_var('theme_style', $theme_style);
    $variables['button_style'] = _lgitTheme_var('button_style', $button_style);
  }
}

function lgit_preprocess_paragraph(&$variables)
{

  $paragraphType = $variables['elements']['#paragraph']->get('type')->__get('target_id');
  $prefix = base_path() . \Drupal::service('extension.path.resolver')->getPath('theme', 'lgit');

  if ($paragraphType == 'offer_teaser_group_element') {
    $variables['content']['theme_style'] = _lgitTheme_var('theme_style');
    $variables['content']['button_style'] = _lgitTheme_var('button_style');
  }

  if ($paragraphType == 'luxair_to_go_countdown') {
    // Get the config based on the API ENDPOINT
    $storage = \Drupal::entityTypeManager()->getStorage('luxair_api_endpoint_entity');
    // Risk on out-of-date FlyNow/ToGo information in case of prolonged post-prod deploy drupal caching.
    $luxair_campaigns_config_endpoint = $storage->load('luxair_campaigns_config_api_url');
    $luxaircalendar_endpoint = $storage->load('luxair_calendar_api_url');
    $luxair_campaigns_Config = file_get_contents($luxair_campaigns_config_endpoint->getUri());
    $variables['content']['config'] = $luxair_campaigns_Config;
    $variables['content']['calendarURL'] = $luxaircalendar_endpoint->getUri();

    // waiting for Drupal || DrupalSettings may be problematic ... and needless.
    $variables['content']['langcode'] = $language = \Drupal::languageManager()->getCurrentLanguage()->getId();
    $variables['content']['images'] = [
      'desktop' => $prefix . '/images/Banner-desktop.svg',
      'tablet' => $prefix . '/images/Banner-tablet.svg',
      'mobile' => $prefix . '/images/Banner-mobile.svg',
      'background_desktop' => $prefix . '/images/Background-desk.svg',
      'background_tablet' => $prefix . '/images/Background-tablet.svg',
      'background_mobile' => $prefix . '/images/Background-mobile.svg',
    ];
  }

  if ($paragraphType == 'no_found_page') {
    $variables['content']['images'] = [
      'image_404' => $prefix . '/images/404.svg'
    ];

    $variables['content']['resources'] = [
      'animation_mobile' => $prefix . '/resources/404-animation-mobile.json',
      'animation_desktop' => $prefix . '/resources/404-animation-desktop.json'
    ];
  }
}

function lgit_preprocess_menu__footer_info(&$variables)
{

  $form = \Drupal::formBuilder()->getForm('Drupal\luxair_applications\Plugin\ApplicationPlugin\Form\ApplicationNewsletterForm');

  $variables['newsletter_form'] = $form;
}

function lgit_preprocess_menu__footer_security(&$variables)
{

  $config = \Drupal::config('luxair_phonebox.settings');

  $variables['phone_display'] = $config->get('phone_display');
  $variables['phone_link'] = $config->get('phone_link');
  $variables['hours'] = $config->get('hours');
}

function lgit_preprocess_field__field_faq_items(&$variables)
{

  $variables['content'] = [];

  foreach ($variables['items'] as $item) {

    //ICON
    $icon = $item['content']['#node']->get('field_teaser_svg_icon')->getValue();
    $file = \Drupal\file\Entity\File::load($icon[0]['target_id']);
    $icon_src = \Drupal::service('file_url_generator')->generateAbsoluteString($file->getFileUri());
    $icon_desc = $icon[0]['description'];

    //MENU TITLE
    $menu_title = $item['content']['#node']->get('field_menu_teaser_title')->getValue()[0]['value'];

    //MENU TEXT
    $menu_text = $item['content']['#node']->get('field_menu_teaser_text')->getValue()[0]['value'];

    $variables['content'][] = [
      'url' => $item['content']['#node']->toUrl(),
      'icon' => [
        'uri' => $icon_src,
        'description' => $icon_desc
      ],
      'title' => $menu_title,
      'text' => $menu_text
    ];
  }
}

function lgit_theme_suggestions_alter(array &$suggestions, array $variables, $hook)
{
  if ($hook == 'form' & !empty($variables['element']['#id'])) {
    $suggestions[] = 'form__' . str_replace('-', '_', $variables['element']['#id']);
  }
}

/**
 * @param $variables
 * @param $hook
 * Generate custom link for FAQ categories
 */
function lgit_preprocess_field__field_categories(&$variables, $hook)
{
  foreach ($variables['items'] as $index => $item) {
    $tid = $item['content']['#options']['entity']->id();
    $url = Url::fromUserInput('/faq/categories/' . $tid);
    $variables['items'][$index]['content']['#url'] = $url;
  }
}

function lgit_preprocess_paragraph__luxair_stage_teaser__stage(&$variables)
{
  $luxair_api_uri = LuxairApiEndpointEntity::load('travel_ibe_products_api_url')->getUri();
  $variables['content']['travel_ibe_products_api_url'] = $luxair_api_uri;
  $variables['language'] = \Drupal::languageManager()->getCurrentLanguage()->getId();
}

function lgit_preprocess_paragraph__info_page(&$variables)
{
  $file_url_generator = \Drupal::service('file_url_generator');
  $langcode = $variables['paragraph']->language()->getId();
  $theme = \Drupal::service('theme.manager')->getActiveTheme()->getName();

  $variables['info_page_urls'] = [];
  $info_page_urls = &$variables['info_page_urls'];
  $variables['info_page_data'] = [
    [
      'title' => $variables['paragraph']->field_hot_topic_title->value,
      'isHotTopic' => TRUE,
      'cards' => [],
    ],
  ];
  $hot_topic_cards = &$variables['info_page_data'][0]['cards'];
  foreach ($variables['paragraph']?->field_info_page_block?->referencedEntities() ?? [] as $info_page_block) {
    $info_page_block = $info_page_block->hasTranslation($langcode)
      ? $info_page_block->getTranslation($langcode)
      : $info_page_block;

    $cards = array_map(function ($info_page_block_element) use ($langcode, $file_url_generator, &$info_page_urls, $theme, &$hot_topic_cards) {
      $info_page_block_element = $info_page_block_element->hasTranslation($langcode)
        ? $info_page_block_element->getTranslation($langcode)
        : $info_page_block_element;

      $info_page_urls[] = $link = $info_page_block_element->field_link->first()->getUrl()->toString();
      $info_page_urls[] = $icon = ($uri = $info_page_block_element->field_svg_icon->entity->getFileUri())
        ? $file_url_generator->generateString($uri)
        : NULL;

      $card = [
        'title' => $info_page_block_element->field_ipbe_title->value,
        'description' => $info_page_block_element->field_ipbe_description->value,
        'icon' => $icon,
        'link' => $link,
        'theme' => $theme,
      ];

      if ($info_page_block_element->field_is_hot_topic->value) {
        $hot_topic_cards[] = $card;
      }

      return $card;
    }, $info_page_block?->field_info_page_blocks_element?->referencedEntities() ?? []);


    $variables['info_page_data'][] = [
      'title' => $info_page_block->field_title->value,
      'isHotTopic' => FALSE,
      'cards' => $cards,
    ];
  }
}

/**
 * Implements hook_preprocess_HOOK().
 *
 * Used to pass the position of the teaser to the template and to bind the
 * teaser cache tag to the paragraph.
 */
function lgit_preprocess_paragraph__teaser(&$variables)
{
  $paragraph = $variables['paragraph'];
  $position = $paragraph->field_position->value;
  $variables['content']['field_teaser'][0]['#cache']['tags'][] = "paragraph:{$paragraph->id()}";
  $variables['content']['field_teaser'][0]['teaser_position'] = ['#markup' => $position];
}
