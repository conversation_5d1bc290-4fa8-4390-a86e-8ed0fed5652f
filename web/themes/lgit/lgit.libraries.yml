main:
  css:
    theme:
      cobrowsing/surfly-cobrowsing-style.css: {}

accessPointTracking:
  js:
    scripts/access-point-tracking.js:
      minified: false
  dependencies:
    - core/drupalSettings

luxon:
  js:
    https://cdn.jsdelivr.net/npm/luxon@2.0.2/build/global/luxon.min.js:
      type: external
      minified: true

flyNow:
  js:
    scripts/fly-now.js:
      minified: false
  dependencies:
    - core/drupalSettings

co2Data:
  header: true # parsed to scope:header
  js:
    scripts/co2-data.js:
      preprocess: false
      minified: true

ldoImages:
  js:
    scripts/luxair-destination-overview-images.js:
      minified: false
  dependencies:
    - core/drupalSettings # indirect

destinationAvailableCalendar:
  js:
    scripts/luxair-destinations-available-calendar.js:
      minified: false
  css:
    theme:
      styles/available-calendar.css:
        minified: false
  dependencies:
    - core/drupalSettings # indirect

noFound:
  js:
    scripts/no-found.js:
      minified: false

lottiePlayer:
  js:
    https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js:
      type: external
      minified: true
  dependencies:
    - core/drupalSettings

lazyLoadBackground:
  js:
    scripts/lazy-load-background.js:
      minified: false
  dependencies:
    - core/once
