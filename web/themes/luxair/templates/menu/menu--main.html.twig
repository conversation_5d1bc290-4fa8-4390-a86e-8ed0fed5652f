{#
/**
 * @file
 * LGIT theme implementation to display a menu.
 *
 * Available variables:
 * - menu_name: The machine name of the menu.
 * - items: A nested list of menu items. Each menu item contains:
 *   - attributes: HTML attributes for the menu item.
 *   - below: The menu item child items.
 *   - title: The menu link title.
 *   - url: The menu link url, instance of \Drupal\Core\Url
 *   - localized_options: Menu link localized options.
 *   - is_expanded: TRUE if the link has visible children within the current
 *     menu tree.
 *   - is_collapsed: TRUE if the link has children within the current menu tree
 *     that are not currently visible.
 *   - in_active_trail: TRUE if the link is in the active trail.
 *
 * @ingroup themeable
 */
#}
<nav
        class="nav"
        data-ng-controller="lgSwitchSetController"
        data-max="1">

    {% for item in items %}
      {% set wrapper_attributes = create_attribute() %}
      {% set nav_link_class = 'nav__link--' ~ item.menuItemMachineName %}
      {% set additional_classes = (item.menuItemMachineName == 'callus' or item.menuItemMachineName == 'contactus') ? 'hidden-on-desktop' : '' %}
      {% set wrapper_attributes = wrapper_attributes.addClass('nav__link', nav_link_class, 'italic', additional_classes) %}

      <div{{ wrapper_attributes }}>
          {% set classStr = 'nav__link__name' %}
          {% for attribute in item.attributes.class %}
              {% set classStr = classStr ~ ' ' ~ attribute %}
          {% endfor %}

          {% if item.url == '#' %}
            <div
                    class="{{ classStr }} {{ item.menuItemMachineName }}"
                    data-ng-click="onItemClick($event, '{{ item.menuItemMachineName }}', '{{ item.menuItemMachineName }}', '{{ item.megadropdownAjax }}')"
                    data-ng-class="{ active : isItemActive('{{ item.menuItemMachineName }}') }">
          <span>
            {{ item.title }}
          </span>
              <span class="itemImg">
        </span>
            </div>

            <div
                    id="megadropdown-{{ item.megadropdownId }}-{{ item.menuItemMachineName }}"
                    class="nav__link__inner bgcolor-pampas {{ item.menuItemMachineName }}"
                    data-ng-class="{ active : isItemActive('{{ item.menuItemMachineName }}') }"
                    style="">
              <div class="nav__link__content__close " data-ng-click="onItemClick($event, '{{ item.menuItemMachineName }}')">
                <svg width="12" height="13" viewBox="0 0 12 13">
                  <path d="M1.312 11.043l9.224-9.224m-.04 9.673l-9.22-9.216" stroke="#504d41" stroke-width="2" fill="none" stroke-linecap="round"/>
                </svg>
              </div>
                {% if item.megadropdownAjax %}
                  <ng-include
                          src="'/mega/get/{{ item.megadropdownId }}'">
                  </ng-include>
                {% else %}
                    {{ item.megadropdownContent }}
                {% endif %}
            </div>
          {% else %}
            <a href="{{ item.url }}" class="nav__link__name {{ item.menuItemMachineName }}">
              <span>{{ item.title }}</span>
            </a>
          {% endif %}
      </div>
    {% endfor %}

    <div class="nav__links--languages">
      {{ language_links }}
    </div>
</nav>
